from flask import Blueprint, request, jsonify
from app.services.event_service import EventService
from app.repositories.event_repository import EventRepository
from app.repositories.customer_repository import CustomerRepository
from app.repositories.user_repository import UserRepository
from app.utils.security import role_required, roles_required
from app.utils.rate_limit import rate_limit
from app.utils.cache_decorators import cached_list, cached_detail, cached_search
from app.schemas.event_schema import event_schema, events_schema
import logging
from marshmallow import ValidationError

event_bp = Blueprint("event", __name__)
event_service = EventService(
    event_repo=EventRepository(),
    customer_repo=CustomerRepository(),
    user_repo=UserRepository()
)
logger = logging.getLogger(__name__)

@event_bp.route("", methods=["GET"])
@roles_required("administrator", "monteur", "verkoper")
@rate_limit("60/minute")
@cached_list(entity_type="event", timeout=300)  # Cache for 5 minutes
def get_all_events():
    # Check if we're filtering by document_id
    document_id = request.args.get("document_id")
    event_type = request.args.get("event_type")

    if document_id:
        try:
            # Get events by document_id
            events = event_service.get_events_by_document(
                document_id=int(document_id),
                event_type=event_type
            )
            logger.info(f"Fetched events for document {document_id}, type {event_type}")
            return jsonify({"events": events}), 200
        except Exception as e:
            logger.error(f"Failed to fetch events for document {document_id}: {str(e)}")
            return jsonify({"error": str(e)}), 500

    # If no document_id, get all events with pagination
    page = int(request.args.get("page", 1))
    per_page = int(request.args.get("per_page", 20))
    try:
        events_data = event_service.get_all_events(page, per_page)
        logger.info(f"Fetched events: page={page}, per_page={per_page}")
        return jsonify(events_data), 200
    except Exception as e:
        logger.error(f"Failed to fetch events: {str(e)}")
        return jsonify({"error": str(e)}), 500

@event_bp.route("/my-events", methods=["GET"])
@roles_required("administrator", "monteur", "verkoper")
@rate_limit("60/minute")
@cached_list(entity_type="user_events", timeout=300)  # Cache for 5 minutes
def get_events_by_user():
    page = int(request.args.get("page", 1))
    per_page = int(request.args.get("per_page", 20))
    try:
        user = request.current_user  # Use request.current_user
        events_data = event_service.get_events_by_user(user.id, page, per_page)
        logger.info(f"Fetched events for user {user.email}: page={page}, per_page={per_page}")
        return jsonify(events_data), 200
    except Exception as e:
        logger.error(f"Failed to fetch events for user: {str(e)}")
        return jsonify({"error": str(e)}), 500

@event_bp.route("/<int:event_id>", methods=["GET"])
@role_required("administrator")
@rate_limit("60/minute")
@cached_detail(entity_type="event", timeout=300)  # Cache for 5 minutes
def get_event(event_id):
    try:
        event = event_service.get_event_by_id(event_id)
        return jsonify(event), 200
    except Exception as e:
        logger.error(f"Failed to fetch event {event_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404

@event_bp.route("", methods=["POST"])
@role_required("administrator")
@rate_limit("60/minute")
def create_event():
    data = request.get_json()
    if not data:
        return jsonify({"error": "No data provided"}), 400

    try:
        # Validate the input data using the schema
        errors = event_schema.validate(data)
        if errors:
            logger.warning(f"Create event validation failed: {errors}")
            return jsonify({"error": "Validation failed", "details": errors}), 400

        # Handle both user_ids (new) and user_id (legacy) for backward compatibility
        user_ids = data.get("user_ids")
        if not user_ids and data.get("user_id"):
            user_ids = [data.get("user_id")]

        event = event_service.create_event(
            event_type=data["event_type"],
            description=data["description"],
            scheduled_date=data["scheduled_date"],
            customer_id=data.get("customer_id"),
            user_ids=user_ids,
            document_id=data.get("document_id")
        )
        logger.info(f"Created event: {event['id']}")
        return jsonify(event), 201
    except ValidationError as e:
        logger.warning(f"Create event validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to create event: {str(e)}")
        return jsonify({"error": str(e)}), 400

@event_bp.route("/<int:event_id>", methods=["PUT"])
@role_required("administrator")
@rate_limit("60/minute")
def update_event(event_id):
    data = request.get_json()
    if not data:
        return jsonify({"error": "No data provided"}), 400

    try:
        # Get the existing event to validate partial updates
        existing_event = event_service.get_event_by_id(event_id)
        if not existing_event:
            logger.warning(f"Update event failed: Event {event_id} not found")
            return jsonify({"error": f"Event with ID {event_id} not found"}), 404

        # Validate the input data using the schema
        errors = event_schema.validate(data)
        if errors:
            logger.warning(f"Update event validation failed: {errors}")
            return jsonify({"error": "Validation failed", "details": errors}), 400

        # Handle both user_ids (new) and user_id (legacy) for backward compatibility
        user_ids = data.get("user_ids")
        if not user_ids and data.get("user_id"):
            user_ids = [data.get("user_id")]

        event = event_service.update_event(
            event_id=event_id,
            event_type=data["event_type"],
            description=data["description"],
            scheduled_date=data["scheduled_date"],
            customer_id=data.get("customer_id"),
            user_ids=user_ids,
            document_id=data.get("document_id")
        )
        logger.info(f"Updated event: {event['id']}")
        return jsonify(event), 200
    except ValidationError as e:
        logger.warning(f"Update event validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to update event {event_id}: {str(e)}")
        return jsonify({"error": str(e)}), 400

@event_bp.route("/<int:event_id>/complete", methods=["POST"])
@roles_required("monteur", "administrator", "verkoper")
@rate_limit("60/minute")
def complete_event(event_id):
    try:
        event = event_service.complete_event(event_id)
        logger.info(f"Completed event: {event['id']}")
        return jsonify(event), 200
    except Exception as e:
        logger.error(f"Failed to complete event {event_id}: {str(e)}")
        return jsonify({"error": str(e)}), 400

@event_bp.route("/<int:event_id>", methods=["DELETE"])
@role_required("administrator")
@rate_limit("60/minute")
def delete_event(event_id):
    try:
        event_service.delete_event(event_id)
        logger.info(f"Deleted event: {event_id}")
        return jsonify({"message": "Event deleted"}), 200
    except Exception as e:
        logger.error(f"Failed to delete event {event_id}: {str(e)}")
        return jsonify({"error": str(e)}), 400

@event_bp.route("/dashboard-metrics", methods=["GET"])
@role_required("administrator")
@rate_limit("60/minute")
@cached_list(entity_type="dashboard_metrics", timeout=300)  # Cache for 5 minutes
def get_dashboard_metrics():
    try:
        metrics = event_service.get_dashboard_metrics()
        logger.info("Fetched dashboard metrics")
        return jsonify(metrics), 200
    except Exception as e:
        logger.error(f"Failed to fetch dashboard metrics: {str(e)}")
        return jsonify({"error": str(e)}), 500

@event_bp.route("/calendar", methods=["GET"])
@roles_required("administrator", "monteur", "verkoper")
@rate_limit("60/minute")
@cached_list(entity_type="calendar_events", timeout=300)  # Cache for 5 minutes
def get_calendar_events():
    try:
        start_date = request.args.get("start_date")
        end_date = request.args.get("end_date")
        user_id = request.args.get("user_id")

        # If user is not admin, only show their events
        if request.current_user.role != "administrator" and user_id and int(user_id) != request.current_user.id:
            user_id = request.current_user.id
        elif request.current_user.role != "administrator" and not user_id:
            user_id = request.current_user.id

        events = event_service.get_calendar_events(
            start_date=start_date,
            end_date=end_date,
            user_id=int(user_id) if user_id else None
        )
        logger.info(f"Fetched calendar events: start={start_date}, end={end_date}, user={user_id}")
        return jsonify({"events": events}), 200
    except Exception as e:
        logger.error(f"Failed to fetch calendar events: {str(e)}")
        return jsonify({"error": str(e)}), 500

@event_bp.route("/quotation/<int:quotation_id>", methods=["GET"])
@roles_required("administrator", "monteur", "verkoper")
@rate_limit("60/minute")
@cached_list(entity_type="quotation_events", timeout=300)  # Cache for 5 minutes
def get_events_by_quotation(quotation_id):
    try:
        events = event_service.get_events_by_quotation(quotation_id)
        logger.info(f"Fetched events for quotation {quotation_id}")
        return jsonify({"events": events}), 200
    except Exception as e:
        logger.error(f"Failed to fetch events for quotation {quotation_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500
