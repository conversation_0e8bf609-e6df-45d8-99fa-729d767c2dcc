import React, { useState, useEffect } from 'react';
import { Product } from '../../types/product';
import {
  getAllProducts,
  searchProducts,
  deleteProduct,
  exportProducts,
  getProductsByCategory,
  deleteAllProducts,
  deleteProductsByCategory
} from '../../services/productService';
import { useConfirmation } from '../../context/ConfirmationContext';
import { useAuth } from '../../context/AuthContext';
import { FaSearch, FaEdit, FaTrash, FaDownload, FaPlus, FaExclamationTriangle } from 'react-icons/fa';
import LoadingSpinner from '../LoadingSpinner';
import Pagination from '../Pagination';
import ProductForm from './ProductForm';
import CategoryFilter from './CategoryFilter';

const ProductList: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [perPage] = useState(20);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const { showConfirmation } = useConfirmation();
  const { user } = useAuth();

  useEffect(() => {
    fetchProducts();
  }, [page, selectedCategory]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError(null);

      let response;
      if (searchTerm) {
        response = await searchProducts(searchTerm, page, perPage, selectedCategory || undefined);
      } else if (selectedCategory) {
        response = await getProductsByCategory(selectedCategory, page, perPage);
      } else {
        response = await getAllProducts(page, perPage);
      }

      setProducts(response.products);
      setTotalItems(response.total);
      setTotalPages(Math.ceil(response.total / perPage));
    } catch (err: any) {
      console.error('Failed to fetch products:', err);
      setError(err.response?.data?.error || 'Fout bij ophalen van producten');
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryChange = (category: string | null) => {
    setSelectedCategory(category);
    setPage(1); // Reset to first page when changing category
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    fetchProducts();
  };

  const handleDelete = (product: Product) => {
    showConfirmation({
      title: 'Product verwijderen',
      message: `Weet je zeker dat je het product "${product.name}" wilt verwijderen?`,
      confirmText: 'Verwijderen',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          await deleteProduct(product.id);
          fetchProducts();
        } catch (err: any) {
          console.error('Failed to delete product:', err);
          setError(err.response?.data?.error || 'Fout bij verwijderen van product');
        }
      }
    });
  };

  const handleDeleteAllProducts = () => {
    showConfirmation({
      title: 'Alle producten verwijderen',
      message: 'Weet je zeker dat je ALLE producten wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt!',
      confirmText: 'Verwijderen',
      cancelText: 'Annuleren',
      confirmButtonClass: 'btn-danger',
      icon: <FaExclamationTriangle className="text-red-500 text-4xl mb-4" />,
      onConfirm: async () => {
        try {
          setLoading(true);
          const result = await deleteAllProducts();
          setError(null);
          fetchProducts();
          return { success: true, message: `${result.count} producten zijn verwijderd.` };
        } catch (err: any) {
          console.error('Failed to delete all products:', err);
          setError(err.response?.data?.error || 'Fout bij verwijderen van alle producten');
          return { success: false, message: err.response?.data?.error || 'Fout bij verwijderen van alle producten' };
        } finally {
          setLoading(false);
        }
      }
    });
  };

  const handleDeleteProductsByCategory = () => {
    if (!selectedCategory) {
      setError('Selecteer eerst een categorie om te verwijderen');
      return;
    }

    showConfirmation({
      title: 'Producten in categorie verwijderen',
      message: `Weet je zeker dat je alle producten in de categorie "${selectedCategory}" wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt!`,
      confirmText: 'Verwijderen',
      cancelText: 'Annuleren',
      confirmButtonClass: 'btn-danger',
      icon: <FaExclamationTriangle className="text-red-500 text-4xl mb-4" />,
      onConfirm: async () => {
        try {
          setLoading(true);
          const result = await deleteProductsByCategory(selectedCategory);
          setError(null);
          fetchProducts();
          return { success: true, message: `${result.count} producten in categorie "${result.category}" zijn verwijderd.` };
        } catch (err: any) {
          console.error('Failed to delete products by category:', err);
          setError(err.response?.data?.error || 'Fout bij verwijderen van producten in categorie');
          return { success: false, message: err.response?.data?.error || 'Fout bij verwijderen van producten in categorie' };
        } finally {
          setLoading(false);
        }
      }
    });
  };

  const handleExport = async () => {
    try {
      const blob = await exportProducts();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `producten_export_${new Date().toISOString().slice(0, 10)}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err: any) {
      console.error('Failed to export products:', err);
      setError(err.response?.data?.error || 'Fout bij exporteren van producten');
    }
  };

  const handleAddSuccess = () => {
    setShowAddForm(false);
    fetchProducts();
  };

  const handleEditSuccess = () => {
    setEditingProduct(null);
    fetchProducts();
  };

  if (showAddForm) {
    return <ProductForm onCancel={() => setShowAddForm(false)} onSuccess={handleAddSuccess} />;
  }

  if (editingProduct) {
    return <ProductForm product={editingProduct} onCancel={() => setEditingProduct(null)} onSuccess={handleEditSuccess} />;
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
        <form onSubmit={handleSearch} className="flex-1">
          <div className="relative">
            <input
              type="text"
              placeholder="Zoek op naam, omschrijving of productcode..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input w-full pl-10"
            />
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </form>

        <div className="flex flex-wrap gap-2">
          <CategoryFilter
            selectedCategory={selectedCategory}
            onCategoryChange={handleCategoryChange}
          />

          <button
            onClick={handleExport}
            className="btn btn-outline"
            title="Exporteer producten naar Excel"
          >
            <FaDownload className="mr-2" /> Exporteren
          </button>

          <button
            onClick={() => setShowAddForm(true)}
            className="btn btn-primary"
          >
            <FaPlus className="mr-2" /> Product toevoegen
          </button>
        </div>
      </div>

      {/* Admin actions - only visible for administrators */}
      {user?.role === 'administrator' && (
        <div className="bg-gray-50 dark:bg-dark-card border border-gray-200 dark:border-gray-700 rounded-lg p-4 mt-4">
          <h3 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-3">Beheerderacties</h3>
          <div className="flex flex-wrap gap-2">
            {selectedCategory ? (
              <button
                onClick={handleDeleteProductsByCategory}
                className="btn btn-danger"
                title={`Verwijder alle producten in categorie "${selectedCategory}"`}
              >
                <FaTrash className="mr-2" /> Verwijder alle producten in categorie "{selectedCategory}"
              </button>
            ) : (
              <button
                onClick={handleDeleteAllProducts}
                className="btn btn-danger"
                title="Verwijder alle producten"
              >
                <FaTrash className="mr-2" /> Verwijder alle producten
              </button>
            )}
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
            <FaExclamationTriangle className="inline-block mr-1" /> Waarschuwing: Het verwijderen van producten kan niet ongedaan worden gemaakt.
          </p>
        </div>
      )}

      {error && (
        <div className="bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 p-3 rounded-md">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      ) : products.length === 0 ? (
        <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">
            {searchTerm ? 'Geen producten gevonden voor deze zoekopdracht.' : 'Geen producten beschikbaar.'}
          </p>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="w-full bg-white dark:bg-dark-card shadow-md rounded-lg">
              <thead className="bg-gray-50 dark:bg-dark-secondary">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Productcode
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Naam
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Categorie
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Brutoprijs
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Korting
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Nettoprijs
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Verkoopprijs
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Acties
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {products.map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50 dark:hover:bg-dark-secondary">
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-amspm-text dark:text-dark-text">
                      {product.product_code || '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-amspm-text dark:text-dark-text">
                      {product.name}
                    </td>
                    <td className="px-4 py-3 text-sm text-amspm-text dark:text-dark-text">
                      {product.category || '-'}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-amspm-text dark:text-dark-text">
                      € {product.gross_price ? product.gross_price.toFixed(2) : '-'}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-amspm-text dark:text-dark-text">
                      {product.discount_percentage ? `${product.discount_percentage}%` : '-'}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-amspm-text dark:text-dark-text">
                      € {product.net_price ? product.net_price.toFixed(2) : '-'}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-amspm-primary dark:text-dark-accent font-medium">
                      € {product.selling_price ? product.selling_price.toFixed(2) : '-'}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                      <button
                        onClick={() => setEditingProduct(product)}
                        className="text-amspm-primary dark:text-dark-accent hover:text-amspm-secondary mr-3"
                        title="Bewerken"
                      >
                        <FaEdit />
                      </button>
                      <button
                        onClick={() => handleDelete(product)}
                        className="text-red-500 hover:text-red-700"
                        title="Verwijderen"
                      >
                        <FaTrash />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <Pagination
            currentPage={page}
            totalPages={totalPages}
            onPageChange={setPage}
            totalItems={totalItems}
            itemsPerPage={perPage}
          />
        </>
      )}
    </div>
  );
};

export default ProductList;
