/* Modal Styles */
.modal-overlay {
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 50;
  animation: fadeIn 0.2s ease-out;
  padding: 1rem;
}

.modal-content {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  animation: slideIn 0.3s ease-out;
  max-width: 95%;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  padding: 1rem;
}

@media (min-width: 480px) {
  .modal-content {
    max-width: 90%;
    padding: 1.25rem;
  }
}

@media (min-width: 640px) {
  .modal-content {
    max-width: 32rem;
    padding: 1.5rem;
  }
}

/* Dark mode styles */
.dark .modal-content {
  background-color: var(--dark-secondary);
  color: var(--dark-text);
  border-color: var(--dark-border);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Form elements within modals */
.modal-content .form-group {
  margin-bottom: 1.25rem;
}

.modal-content label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--amspm-primary);
}

.dark .modal-content label {
  color: var(--dark-text-light);
}

.modal-content .input,
.modal-content select,
.modal-content textarea {
  width: 100%;
  padding: 0.625rem 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  background-color: white;
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  min-height: 40px;
  touch-action: manipulation;
}

.modal-content .input:focus,
.modal-content select:focus,
.modal-content textarea:focus {
  border-color: var(--amspm-primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--amspm-primary-rgb), 0.2);
}

/* Modal form layouts */
.modal-content .form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .modal-content .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.modal-content .form-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

@media (min-width: 480px) {
  .modal-content .form-actions {
    flex-direction: row;
    justify-content: flex-end;
  }
}

/* Dark mode form elements */
.dark .modal-content .input,
.dark .modal-content select,
.dark .modal-content textarea {
  background-color: var(--dark-input);
  border-color: var(--dark-border);
  color: var(--dark-text);
}

.dark .modal-content .input:focus,
.dark .modal-content select:focus,
.dark .modal-content textarea:focus {
  border-color: var(--amspm-primary);
  box-shadow: 0 0 0 3px rgba(var(--amspm-primary-rgb), 0.3);
}

/* Modal buttons */
.modal-content .btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;
}

.modal-content .btn-primary {
  background-color: var(--amspm-primary);
  color: white;
}

.modal-content .btn-primary:hover {
  background-color: var(--amspm-primary-dark);
}

.modal-content .btn-secondary {
  background-color: var(--amspm-secondary);
  color: white;
}

.modal-content .btn-secondary:hover {
  background-color: var(--amspm-secondary-dark);
}

.modal-content .btn-outline {
  background-color: transparent;
  border: 1px solid #e2e8f0;
  color: #4a5568;
}

.modal-content .btn-outline:hover {
  background-color: #f7fafc;
}

.dark .modal-content .btn-outline {
  border-color: var(--dark-border-light);
  color: var(--dark-text);
}

.dark .modal-content .btn-outline:hover {
  background-color: var(--dark-hover);
}

/* Disabled buttons */
.modal-content .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Modal header */
.modal-content .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.dark .modal-content .modal-header {
  border-bottom-color: var(--dark-border);
}

.modal-content .modal-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--amspm-primary);
}

.dark .modal-content .modal-header h2 {
  color: var(--dark-accent);
  font-weight: 600;
}

/* Modal footer */
.modal-content .modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.dark .modal-content .modal-footer {
  border-top-color: var(--dark-border);
}

/* Modal body */
.modal-content .modal-body {
  padding: 1.5rem;
}

/* Error messages */
.modal-content .error-message {
  background-color: #fff5f5;
  border-left: 4px solid #f56565;
  color: #c53030;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 0.25rem;
}

.dark .modal-content .error-message {
  background-color: rgba(239, 68, 68, 0.15);
  border-left: 4px solid var(--dark-error);
  color: #fca5a5;
}

/* Success messages */
.modal-content .success-message {
  background-color: #f0fff4;
  border-left: 4px solid #48bb78;
  color: #2f855a;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 0.25rem;
}

.dark .modal-content .success-message {
  background-color: rgba(16, 185, 129, 0.15);
  border-left: 4px solid var(--dark-success);
  color: #6ee7b7;
}
