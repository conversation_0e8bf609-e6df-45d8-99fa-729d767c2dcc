import React, { useState } from "react";
import { useAuth } from "../context/AuthContext";
import { useNavigate } from "react-router-dom";
import { FaLock, FaEnvelope } from "react-icons/fa";
import { loginSchema, validateData } from "../utils/validation";
import * as authService from "../services/authService";

const Login: React.FC = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [showResetModal, setShowResetModal] = useState(false);
  const [resetEmail, setResetEmail] = useState("");
  const [resetSuccess, setResetSuccess] = useState(false);
  const [resetError, setResetError] = useState<string | null>(null);
  const [isSubmittingReset, setIsSubmittingReset] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setValidationErrors([]);

    // Validate input data
    const { isValid, errors } = await validateData(loginSchema, { email, password });

    if (!isValid) {
      setValidationErrors(errors);
      return;
    }

    try {
      const userData = await login(email, password);
      if (userData.role === "administrator") {
        navigate("/dashboard");
      } else if (userData.role === "unknown") {
        // This is a fallback user from a canceled token verification
        setError("Connection to server timed out. You are logged in with limited functionality. Please try again later.");
        // Still navigate to a safe page
        navigate("/user-dashboard");
      } else {
        navigate("/user-dashboard");
      }
    } catch (err: any) {
      // Handle different error types
      if (err?.name === 'CanceledError' || err?.code === 'ERR_CANCELED') {
        setError("Connection to server timed out. Please try again.");
      } else if (err?.code === 'auth/wrong-password' || err?.code === 'auth/user-not-found') {
        setError("Invalid email or password. Please check your credentials and try again.");
      } else if (err?.code === 'auth/too-many-requests') {
        setError("Too many failed login attempts. Please try again later or reset your password.");
      } else {
        setError("Failed to login. Please check your credentials and try again.");
      }
      console.error(err);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setResetError(null);
    setResetSuccess(false);
    setIsSubmittingReset(true);

    try {
      if (!resetEmail || !resetEmail.includes('@')) {
        setResetError("Voer een geldig e-mailadres in");
        setIsSubmittingReset(false);
        return;
      }

      await authService.resetPassword(resetEmail);
      setResetSuccess(true);
      // Optioneel: sluit het modal na een succesvolle reset
      setTimeout(() => {
        setShowResetModal(false);
        setResetEmail("");
      }, 3000);
    } catch (err: any) {
      if (err?.code === 'auth/user-not-found') {
        setResetError("Er is geen gebruiker gevonden met dit e-mailadres");
      } else if (err?.code === 'auth/invalid-email') {
        setResetError("Ongeldig e-mailadres");
      } else if (err?.code === 'auth/too-many-requests') {
        setResetError("Te veel aanvragen. Probeer het later opnieuw");
      } else {
        setResetError("Er is een fout opgetreden bij het resetten van het wachtwoord");
      }
      console.error(err);
    } finally {
      setIsSubmittingReset(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-amspm-background px-4 py-6">
      <div className="max-w-md w-full p-4 sm:p-8 bg-white rounded-lg shadow-lg">
        <div className="text-center mb-6 sm:mb-8">
          <div className="flex justify-center mb-3 sm:mb-4">
            <FaLock className="text-amspm-primary" size={48} />
          </div>
          <h1 className="text-xl sm:text-2xl font-bold text-amspm-primary">AMSPM Management</h1>
          <p className="text-sm sm:text-base text-amspm-text-light mt-1 sm:mt-2">Sign in to your account</p>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {validationErrors.length > 0 && (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
            <ul className="list-disc pl-5">
              {validationErrors.map((err, index) => (
                <li key={index}>{err}</li>
              ))}
            </ul>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
          <div>
            <label className="block text-amspm-text font-medium mb-1">Email</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaEnvelope className="text-amspm-text-light" />
              </div>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="input pl-10"
                placeholder="Enter your email"
              />
            </div>
          </div>

          <div>
            <label className="block text-amspm-text font-medium mb-1">Password</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaLock className="text-amspm-text-light" />
              </div>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className="input pl-10"
                placeholder="Enter your password"
              />
            </div>
          </div>

          <button
            type="submit"
            className="w-full bg-amspm-primary text-amspm-secondary py-2 px-4 rounded hover:bg-opacity-90 transition duration-200 flex items-center justify-center"
          >
            Sign In
          </button>

          <div className="mt-4 text-center">
            <button
              type="button"
              onClick={() => setShowResetModal(true)}
              className="text-amspm-primary text-sm hover:underline"
            >
              Wachtwoord vergeten?
            </button>
          </div>
        </form>

        <div className="mt-8 text-center text-sm text-amspm-text-light">
          &copy; {new Date().getFullYear()} AMSPM Customer Management
        </div>
      </div>

      {/* Password Reset Modal */}
      {showResetModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-amspm-text">Wachtwoord resetten</h2>
              <button
                onClick={() => {
                  setShowResetModal(false);
                  setResetEmail("");
                  setResetError(null);
                  setResetSuccess(false);
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                &times;
              </button>
            </div>

            {resetSuccess ? (
              <div className="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded">
                <p>Er is een e-mail verzonden naar {resetEmail} met instructies om je wachtwoord te resetten.</p>
              </div>
            ) : (
              <form onSubmit={handleResetPassword}>
                {resetError && (
                  <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded">
                    <p>{resetError}</p>
                  </div>
                )}

                <div className="mb-4">
                  <label className="block text-amspm-text font-medium mb-1">E-mailadres</label>
                  <input
                    type="email"
                    value={resetEmail}
                    onChange={(e) => setResetEmail(e.target.value)}
                    className="input"
                    placeholder="Voer je e-mailadres in"
                    required
                  />
                  <p className="text-sm text-gray-500 mt-2">
                    Voer het e-mailadres in dat je gebruikt om in te loggen. We sturen je een e-mail met instructies om je wachtwoord te resetten.
                  </p>
                </div>

                <div className="flex justify-end space-x-2">
                  <button
                    type="button"
                    onClick={() => {
                      setShowResetModal(false);
                      setResetEmail("");
                      setResetError(null);
                      setResetSuccess(false);
                    }}
                    className="btn btn-outline"
                  >
                    Annuleren
                  </button>
                  <button
                    type="submit"
                    className="btn btn-secondary"
                    disabled={isSubmittingReset}
                  >
                    {isSubmittingReset ? "Bezig..." : "Wachtwoord resetten"}
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Login;
