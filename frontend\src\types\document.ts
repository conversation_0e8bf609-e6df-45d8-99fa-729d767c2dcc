// frontend/src/types/document.ts
export interface Document {
  id: number;
  customer_id: number;
  customer_name?: string;
  event_id: number | null;
  file_url: string;
  document_type: string;
  uploaded_by: string;
  expiry_date: string | null;
  created_at: string;
  expiry_status: "green" | "orange" | "red";
  status: "active" | "inactive" | "not_applicable";
  sub_documents: Document[];
  related_document_id?: number | null;
}

export interface DocumentFilter {
  expiry_status?: "green" | "orange" | "red";
  active_status?: "active" | "inactive";
  document_type?: string;
}
