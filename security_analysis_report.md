# Security Analysis Report: Customer Management Application

## Executive Summary

This report presents a comprehensive security analysis of the Customer Management Application, which consists of a Flask backend and React frontend. The application is designed to manage customer data, documents, events, and user permissions. The analysis identifies several security strengths, but also reveals critical vulnerabilities and areas for improvement.

Key findings include:
- Strong authentication using Firebase, but with some implementation weaknesses
- Disabled CSRF protection without adequate compensating controls
- Potential data exposure through insecure storage practices
- Inadequate input validation in several critical areas
- Overly permissive rate limiting
- Hardcoded credentials and sensitive information

This report provides detailed recommendations to address these issues and enhance the overall security posture of the application.

## Table of Contents

1. [Authentication and Authorization](#1-authentication-and-authorization)
2. [Input Validation and Data Sanitization](#2-input-validation-and-data-sanitization)
3. [File Upload Security](#3-file-upload-security)
4. [API Security](#4-api-security)
5. [Data Protection](#5-data-protection)
6. [Frontend Security](#6-frontend-security)
7. [Infrastructure and Configuration](#7-infrastructure-and-configuration)
8. [Logging and Monitoring](#8-logging-and-monitoring)
9. [Prioritized Recommendations](#9-prioritized-recommendations)

## 1. Authentication and Authorization

### Strengths
- Firebase Authentication provides a robust identity management system
- Role-based access control (RBAC) is implemented throughout the application
- Token verification for API requests
- Proper token expiration handling
- Fine-grained permission system for document access

### Vulnerabilities

#### 1.1 Disabled CSRF Protection (Critical)
The application has explicitly disabled CSRF protection:
```python
# Configure CSRF protection
# Disable CSRF protection for the entire app since we use token-based authentication
app.config['CSRF_DISABLE'] = True
# Skip CSRF initialization completely
# csrf.init_app(app)
```

While the application uses token-based authentication, CSRF vulnerabilities can still exist, especially since the application uses cookies for session management.

#### 1.2 Insecure Token Storage (High)
User tokens and data are stored in localStorage:
```javascript
// Cache user data in localStorage for fallback during connection issues
localStorage.setItem('user', JSON.stringify(userData));
```

localStorage is vulnerable to XSS attacks, which could lead to token theft.

#### 1.3 Hardcoded Admin Credentials (Critical)
The `set_custom_claims.py` script contains a hardcoded admin email:
```python
admin_email = '<EMAIL>'
```

#### 1.4 Weak Session Management (Medium)
The application sets a session timeout cookie but doesn't properly enforce session expiration:
```python
# Set session timeout (24 hours)
response.set_cookie(
    'session_timeout',
    value='true',
    max_age=86400,  # 24 hours in seconds
    secure=True,
    httponly=True,
    samesite='Strict'
)
```

#### 1.5 Fallback Authentication Mechanism (Medium)
The application implements a fallback mechanism that could allow access with limited verification:
```javascript
if (error?.name === 'CanceledError' || error?.code === 'ERR_CANCELED') {
  // If it's a timeout/canceled error, try to use Firebase user data as fallback
  console.log('Token verification request was canceled (timeout). Using Firebase data as fallback.');

  const fallbackUserData: User = {
    id: 0, // We don't know the actual ID
    email: userCredential.user.email || '',
    role: 'unknown', // We don't know the role
    name: userCredential.user.displayName || '',
    firebase_uid: userCredential.user.uid
  };

  setUser(fallbackUserData);
  return fallbackUserData;
}
```

### Recommendations
1. Re-enable CSRF protection for all state-changing operations
2. Use HttpOnly, Secure cookies instead of localStorage for sensitive data
3. Remove hardcoded credentials and use environment variables
4. Implement proper session management with server-side validation
5. Revise the fallback authentication mechanism to prevent unauthorized access

## 2. Input Validation and Data Sanitization

### Strengths
- Some validation is implemented using Yup schemas
- Database models include validation for certain fields
- Error handling for invalid inputs in many controllers

### Vulnerabilities

#### 2.1 Inconsistent Input Validation (High)
Input validation is inconsistently applied across the application. Some endpoints perform thorough validation:
```python
@customer_bp.route("", methods=["POST"], strict_slashes=False)
@role_required("administrator")
@rate_limit("10000/minute")
def create_customer():
    data = request.get_json()
    if not data or "name" not in data:
        logger.warning("Create customer failed: Missing name")
        return jsonify({"error": "Name is required"}), 400
```

While others lack proper validation:
```python
@document_bp.route("/mock", methods=["POST"])
@roles_required("monteur", "verkoper", "administrator")
@rate_limit("100000/minute")
def create_mock_document():
    try:
        logger.info(f"Received request data: {request.form}")
        customer_id_str = request.form.get("customer_id")
        if not customer_id_str:
            return jsonify({"error": "customer_id is required"}), 400
        customer_id = int(customer_id_str)
```

#### 2.2 Lack of Data Sanitization (High)
The application doesn't consistently sanitize user inputs before using them in database queries or file operations.

#### 2.3 Potential SQL Injection (Critical)
Some database operations use raw SQL queries without proper parameterization:
```python
db.session.execute(text('''
    CREATE TABLE document_templates (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        document_type VARCHAR(50) NOT NULL,
        description TEXT,
        file_path VARCHAR(255) NOT NULL,
        file_type VARCHAR(10) NOT NULL DEFAULT 'pdf',
        created_by INTEGER NOT NULL REFERENCES users(id),
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
    )
'''))
```

### Recommendations
1. Implement consistent input validation across all endpoints
2. Add data sanitization for all user inputs
3. Use parameterized queries for all database operations
4. Implement a centralized validation service
5. Add validation for file uploads, including content type verification

## 3. File Upload Security

### Strengths
- File uploads are restricted to authenticated users
- Role-based permissions for file uploads
- Document type validation

### Vulnerabilities

#### 3.1 Insufficient File Type Validation (Critical)
The application accepts file uploads without proper content type validation:
```javascript
<input
  type="file"
  accept="image/*"
  onChange={handleFileChange}
  className="input"
  disabled={submitting || documentNotApplicable}
/>
```

The `accept` attribute is easily bypassed on the client side.

#### 3.2 Insecure File Storage (High)
Files are stored with predictable paths:
```python
destination_path = f"documents/{customer_id}/{event_id if event_id else 'no_event'}_{document_type}_{int(datetime.utcnow().timestamp())}.jpg"
```

This could allow attackers to guess file URLs.

#### 3.3 Public File Access (High)
Uploaded files are made publicly accessible:
```python
def upload_file_to_storage(file, destination_path: str) -> str:
    if USE_MOCK_FIREBASE:
        print(f"[Mock] Uploading file to {destination_path}")
        return f"https://mock-storage.googleapis.com/{destination_path}"
    else:
        from firebase_admin import storage
        bucket = storage.bucket()
        blob = bucket.blob(destination_path)
        blob.upload_from_file(file)
        blob.make_public()
        return blob.public_url
```

The `make_public()` call makes all files publicly accessible without authentication.

### Recommendations
1. Implement server-side file type validation
2. Use content type detection libraries to verify file contents
3. Generate random, unpredictable file names
4. Implement proper access controls for file storage
5. Serve files through authenticated endpoints instead of making them public

## 4. API Security

### Strengths
- Token-based authentication for API requests
- Role-based access control for API endpoints
- Rate limiting implemented

### Vulnerabilities

#### 4.1 Overly Permissive Rate Limits (Medium)
The rate limits are set extremely high, providing minimal protection against abuse:
```python
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["100000/minute", "1000000/hour"],
    storage_uri="memory://"
)
```

Most endpoints use the same high limits:
```python
@rate_limit("100000/minute")
```

#### 4.2 Insecure CORS Configuration (Medium)
CORS is configured to allow specific origins, but the implementation has issues:
```python
CORS(app,
     supports_credentials=False, # Changed to False to avoid CORS issues with credentials
     resources={r"/*": {"origins": ["https://localhost:5173", "http://localhost:5173"]}},
     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
     allow_headers=["Authorization", "Content-Type", "X-CSRFToken", "Accept", "Origin"],
     expose_headers=["Content-Type", "X-CSRFToken", "Access-Control-Allow-Origin"])
```

The comment suggests CORS was modified to avoid issues rather than for security reasons.

#### 4.3 Verbose Error Messages (Low)
API endpoints often return detailed error messages that could reveal sensitive information:
```python
except Exception as e:
    logger.error(f"Token verification failed: {str(e)}")
    return jsonify({"error": str(e)}), 401
```

### Recommendations
1. Implement more reasonable rate limits based on endpoint sensitivity
2. Configure CORS properly for production environments
3. Implement generic error messages for clients while logging detailed errors server-side
4. Add API versioning for better maintenance
5. Consider implementing API key rotation for external integrations

## 5. Data Protection

### Strengths
- PostgreSQL database for structured data storage
- Firebase for authentication
- HTTPS implementation with SSL certificates

### Vulnerabilities

#### 5.1 Hardcoded Firebase Credentials (Critical)
Firebase service account credentials are included in the repository:
```
Path: backend\secrets\firebase-service-account-key.json
{
    "type": "service_account",
    "project_id": "amspm-a8616",
    "private_key_id": "9344a7d17a714a537789a25cfa67d9d3161c19cd",
...
```

#### 5.2 Debug Mode in Production (High)
The application runs with debug mode enabled:
```python
class Config:
    DEBUG = True
    SECRET_KEY = os.getenv("SECRET_KEY")
```

#### 5.3 Insecure Secret Key Handling (High)
The application uses environment variables for secrets but doesn't validate their presence:
```python
SECRET_KEY = os.getenv("SECRET_KEY")
```

If the environment variable is missing, this will be `None`.

#### 5.4 Lack of Data Encryption (Medium)
Sensitive customer data is stored without encryption:
```python
class Customer(db.Model):
    __tablename__ = "customers"
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.DateTime, nullable=True)
    code = db.Column(db.String(50), nullable=True)
    name = db.Column(db.String(100), nullable=False)
    kvk_number = db.Column(db.String(20), nullable=True)
    contact_person = db.Column(db.String(100), nullable=True)
    gender = db.Column(db.String(1), nullable=True)
```

### Recommendations
1. Remove hardcoded credentials from the repository
2. Use a secrets management solution
3. Disable debug mode in production
4. Implement field-level encryption for sensitive data
5. Validate the presence of required environment variables at startup

## 6. Frontend Security

### Strengths
- React framework with TypeScript for type safety
- Protected routes implementation
- Authentication state management
- Input validation with Yup schemas

### Vulnerabilities

#### 6.1 Insecure Data Storage (High)
Sensitive user data is stored in localStorage:
```javascript
// Cache user data in localStorage for fallback during connection issues
localStorage.setItem('user', JSON.stringify(userData));
```

#### 6.2 Exposed Environment Variables (Medium)
Frontend environment variables are exposed to the client:
```javascript
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID,
};
```

#### 6.3 Lack of Content Security Policy (Medium)
The application doesn't implement a Content Security Policy to prevent XSS attacks.

### Recommendations
1. Use secure, HttpOnly cookies instead of localStorage
2. Implement a proper Content Security Policy
3. Minimize exposure of environment variables in the frontend
4. Add security headers (X-Frame-Options, X-Content-Type-Options, etc.)
5. Implement subresource integrity for external scripts

## 7. Infrastructure and Configuration

### Strengths
- HTTPS implementation with SSL certificates
- PostgreSQL database
- Firebase for authentication and storage

### Vulnerabilities

#### 7.1 Self-Signed Certificates (Medium)
The application uses self-signed certificates:
```python
ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
ssl_context.load_cert_chain(
    certfile=os.path.abspath("./certs/cert.pem"),
    keyfile=os.path.abspath("./certs/key.pem")
)
```

#### 7.2 Debug Mode in Production (High)
The application runs with debug mode enabled:
```python
app.run(host="0.0.0.0", port=5000, ssl_context=ssl_context, debug=True, threaded=True)
```

#### 7.3 Exposed Internal Network (High)
The application binds to all network interfaces:
```python
app.run(host="0.0.0.0", port=5000, ssl_context=ssl_context, debug=True, threaded=True)
```

### Recommendations
1. Use properly signed certificates from a trusted CA
2. Disable debug mode in production
3. Bind to specific interfaces based on deployment environment
4. Implement a proper deployment pipeline with environment-specific configurations
5. Use a reverse proxy (Nginx, Apache) for production deployments

## 8. Logging and Monitoring

### Strengths
- Structured logging implementation
- Audit logging for important actions
- Error logging

### Vulnerabilities

#### 8.1 Sensitive Data in Logs (Medium)
Logs may contain sensitive information:
```python
logger.info(f"Received request data: {request.form}")
```

#### 8.2 Inconsistent Error Handling (Medium)
Error handling is inconsistent across the application, with some errors being logged and others not.

#### 8.3 Lack of Security Monitoring (High)
The application doesn't implement security-specific monitoring or alerting.

### Recommendations
1. Implement a centralized logging solution
2. Filter sensitive data from logs
3. Standardize error handling across the application
4. Add security monitoring and alerting
5. Implement audit logging for all security-relevant events

## 9. Prioritized Recommendations

### Critical (Fix Immediately)
1. Remove hardcoded Firebase credentials from the repository
2. Implement proper file upload validation and security
3. Re-enable CSRF protection or implement proper token-based protection
4. Fix potential SQL injection vulnerabilities
5. Disable debug mode in production

### High (Fix Soon)
1. Implement secure storage for user tokens and data
2. Add consistent input validation across all endpoints
3. Improve error handling to avoid information disclosure
4. Implement proper access controls for file storage
5. Use a secrets management solution

### Medium (Plan to Address)
1. Configure more reasonable rate limits
2. Implement a Content Security Policy
3. Use properly signed SSL certificates
4. Add security headers
5. Improve session management

### Low (Consider Implementing)
1. Add API versioning
2. Implement subresource integrity for external scripts
3. Add more comprehensive audit logging
4. Improve documentation of security features
5. Conduct regular security training for developers

## Conclusion

The Customer Management Application has several security strengths, particularly in its use of Firebase for authentication and its role-based access control system. However, there are significant vulnerabilities that need to be addressed, especially in the areas of credential management, file upload security, and input validation.

By implementing the recommendations in this report, particularly the critical and high-priority items, the security posture of the application can be significantly improved. Regular security assessments should be conducted to ensure that new vulnerabilities are identified and addressed promptly.
