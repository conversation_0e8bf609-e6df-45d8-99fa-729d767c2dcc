import api from "../api";

export interface Activity {
  id: number;
  type: 'user' | 'customer' | 'document' | 'event';
  action: string;
  subject: string;
  timestamp: string;
  user: {
    id: number;
    name: string;
  };
}

export interface GetRecentActivitiesResponse {
  activities: Activity[];
}

export const getRecentActivities = async (limit: number = 10): Promise<GetRecentActivitiesResponse> => {
  try {
    const response = await api.get(`/audit/recent?limit=${limit}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching recent activities:", error);
    // Return mock data for now
    return {
      activities: [
        {
          id: 1,
          type: 'user',
          action: 'created',
          subject: '<PERSON>',
          timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 minutes ago
          user: { id: 1, name: 'Admin User' }
        },
        {
          id: 2,
          type: 'customer',
          action: 'updated',
          subject: 'ACME Corp',
          timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
          user: { id: 1, name: 'Admin User' }
        },
        {
          id: 3,
          type: 'document',
          action: 'uploaded',
          subject: 'Contract Document',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
          user: { id: 2, name: 'Sales User' }
        },
        {
          id: 4,
          type: 'event',
          action: 'scheduled',
          subject: 'Maintenance Visit',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(), // 5 hours ago
          user: { id: 3, name: 'Technician User' }
        },
        {
          id: 5,
          type: 'document',
          action: 'expired',
          subject: 'Safety Certificate',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
          user: { id: 1, name: 'Admin User' }
        }
      ]
    };
  }
};
