import React from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import { FaFileInvoiceDollar } from 'react-icons/fa';
import Breadcrumbs from '../components/Breadcrumbs';
import QuotationList from '../components/quotations/QuotationList';
import QuotationForm from '../components/quotations/QuotationForm';
import QuotationDetail from '../components/quotations/QuotationDetail';

const QuotationsPage: React.FC = () => {
  const location = useLocation();
  const isListView = location.pathname === '/quotations';

  return (
    <div className="container mx-auto px-4 py-8">
      <Breadcrumbs />

      {isListView && (
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-amspm-text dark:text-dark-text flex items-center">
            <FaFileInvoiceDollar className="mr-2" /> Offertes
          </h1>
          <p className="text-gray-600 dark:text-dark-text-light mt-2">
            Beheer offertes voor klanten.
          </p>
        </div>
      )}

      <Routes>
        <Route path="/" element={<QuotationList />} />
        <Route path="/new" element={<QuotationForm />} />
        <Route path="/:id" element={<QuotationDetail />} />
        <Route path="/:id/edit" element={<QuotationForm />} />
      </Routes>
    </div>
  );
};

export default QuotationsPage;
