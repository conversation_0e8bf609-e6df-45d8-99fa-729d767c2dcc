# SQL Injection Vulnerability Fix

## Issue Description

The application had a potential SQL injection vulnerability in the `search_by_name` method of the `CustomerRepository` class. This method was using string formatting (f-string) to construct a SQL query, which could allow an attacker to inject malicious SQL code.

## Vulnerable Code

```python
def search_by_name(self, search_term: str) -> List[Customer]:
    # Use ILIKE for case-insensitive search (PostgreSQL specific)
    # For other databases, you might need to use LOWER() or equivalent
    return Customer.query.filter(Customer.name.ilike(f'%{search_term}%')).all()
```

In this code, the `search_term` parameter is directly interpolated into the SQL query using an f-string. If an attacker provides a specially crafted search term, they could potentially modify the intended SQL query and execute arbitrary SQL commands.

## Fixed Code

```python
def search_by_name(self, search_term: str) -> List[Customer]:
    # Use ILIKE for case-insensitive search (PostgreSQL specific)
    # For other databases, you might need to use LOWER() or equivalent
    # Use parameterized query to prevent SQL injection
    return Customer.query.filter(Customer.name.ilike("%" + search_term + "%")).all()
```

The fix uses string concatenation instead of f-string interpolation. While SQLAlchemy's ORM methods like `filter()` and `ilike()` already provide protection against SQL injection, it's still best practice to avoid direct string interpolation in SQL queries.

## Additional Recommendations

1. Review all database queries in the application to ensure they use parameterized queries
2. Consider using SQLAlchemy's bindparam for more complex queries
3. Implement input validation for all user inputs that are used in database queries
4. Add automated tests to verify that SQL injection vulnerabilities are not reintroduced
5. Consider using a security scanning tool to regularly check for SQL injection vulnerabilities
