# Responsive Design System

This document outlines the responsive design system used in the AMSPM Customer Management application.

> **Note:** For dark mode specific guidelines, see [dark-mode-guide.md](./dark-mode-guide.md)

## Breakpoints

The application uses the following breakpoints (following Tailwind CSS defaults):

- **Small (sm)**: 640px and above
- **Medium (md)**: 768px and above
- **Large (lg)**: 1024px and above
- **Extra Large (xl)**: 1280px and above
- **2XL (2xl)**: 1536px and above

## Responsive Utility Classes

### Layout

- `.responsive-container`: Applies appropriate padding for different screen sizes
- `.responsive-grid`: Creates a responsive grid layout (1 column on mobile, 2 on small screens, 3-4 on larger screens)
- `.responsive-grid-tight`: Similar to responsive-grid but with tighter spacing
- `.responsive-flex`: Switches between column layout on mobile and row layout on larger screens
- `.responsive-card`: Card with appropriate padding for different screen sizes

### Typography

- `.responsive-text`: Text that adjusts size based on screen size (includes dark mode text color)
- `.responsive-text-light`: Lighter text that adjusts size based on screen size (includes dark mode text color)
- `.responsive-heading`: Heading that adjusts size based on screen size (includes dark mode accent color)
- `.responsive-subheading`: Subheading that adjusts size based on screen size (includes dark mode text color)

### Touch Interfaces

- `.touch-target`: Ensures elements are large enough for touch interaction (minimum 44x44px)

## Mobile-First Approach

All styles are written using a mobile-first approach. This means:

1. Base styles are for mobile devices
2. Use media queries with `sm:`, `md:`, `lg:`, etc. to apply styles for larger screens

Example:
```css
.element {
  @apply text-sm p-2 sm:text-base sm:p-4 md:text-lg md:p-6;
}
```

## Tables

For tables, we use two approaches:

1. **Desktop**: Standard HTML tables with responsive widths and horizontal scrolling when needed
2. **Mobile**: Card-based layout that displays each row as a card on small screens

Example:
```jsx
{/* Desktop view */}
<div className="hidden md:block overflow-x-auto">
  <table className="min-w-full">
    {/* Table content */}
  </table>
</div>

{/* Mobile view */}
<div className="md:hidden space-y-4">
  {items.map(item => (
    <div className="mobile-card">
      {/* Card content */}
    </div>
  ))}
</div>
```

## Forms

Forms should use:

- `.form-grid` for multi-column layouts on larger screens
- `.form-row` for horizontal alignment of related inputs on larger screens
- Ensure all inputs have appropriate touch target sizes

## Dark Mode Utilities

The following utility classes help with dark mode compatibility:

- `.dark-mode-text`: Applies appropriate text color in both light and dark modes
- `.dark-mode-text-light`: Applies appropriate secondary text color in both modes
- `.dark-mode-bg`: Applies appropriate background color in both modes
- `.dark-mode-border`: Applies appropriate border color in both modes

## Best Practices

1. Always test on multiple device sizes
2. Use relative units (rem, em, %) instead of fixed units (px) when possible
3. Ensure sufficient spacing between interactive elements on touch devices
4. Use the `touch-manipulation` class for better touch response
5. Ensure text is readable at all screen sizes (minimum 16px for body text)
6. Always include dark mode variants for color-related styles
7. Test both light and dark modes on different device sizes
