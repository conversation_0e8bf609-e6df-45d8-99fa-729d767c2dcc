import os
import json
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    # Set DEBUG based on environment variable, default to False for security
    DEBUG = os.getenv("FLASK_DEBUG", "False").lower() in ("true", "1", "t")
    SECRET_KEY = os.getenv("SECRET_KEY")
    if not SECRET_KEY:
        raise ValueError("No SECRET_KEY set in environment variables")

    SQLALCHEMY_DATABASE_URI = os.getenv("DATABASE_URL")
    if not SQLALCHEMY_DATABASE_URI:
        raise ValueError("No DATABASE_URL set in environment variables")

    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Firebase credentials can be provided either as a path to a JSON file
    # or as a JSON string directly in the environment variables
    FIREBASE_CREDENTIALS_PATH = os.getenv("FIREBASE_CREDENTIALS_PATH")
    FIREBASE_CREDENTIALS_JSON = os.getenv("FIREBASE_CREDENTIALS_JSON")

    # Validate that at least one Firebase credential option is provided
    if not FIREBASE_CREDENTIALS_PATH and not FIREBASE_CREDENTIALS_JSON:
        raise ValueError("No Firebase credentials provided. Set either FIREBASE_CREDENTIALS_PATH or FIREBASE_CREDENTIALS_JSON")

    # Parse the JSON string if provided
    if FIREBASE_CREDENTIALS_JSON:
        try:
            FIREBASE_CREDENTIALS = json.loads(FIREBASE_CREDENTIALS_JSON)
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON in FIREBASE_CREDENTIALS_JSON environment variable")

    LOG_FILE = os.getenv("LOG_FILE", "./logs/app.log")

    # Cache configuration
    CACHE_TYPE = os.getenv("CACHE_TYPE", "SimpleCache")  # Options: SimpleCache, RedisCache
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    CACHE_TIMEOUT = int(os.getenv("CACHE_TIMEOUT", 3600))  # Default: 1 hour
    CACHE_KEY_PREFIX = os.getenv("CACHE_KEY_PREFIX", "customer_mgmt_")

    # Cache version - increment this when making schema changes
    CACHE_VERSION = "1.0"

    # Rate limiting configuration
    RATELIMIT_DEFAULT = os.getenv("RATELIMIT_DEFAULT", "200/minute,5000/hour")

    # Security settings
    # Enable response sanitization by default to hide sensitive information
    SANITIZE_RESPONSES = os.getenv("SANITIZE_RESPONSES", "True").lower() in ("true", "1", "t")

    # Optional e-Boekhouden settings
    EBOEKHOUDEN_API_KEY = os.getenv("EBOEKHOUDEN_API_KEY")
    EBOEKHOUDEN_API_URL = os.getenv("EBOEKHOUDEN_API_URL")
    EBOEKHOUDEN_API_USERNAME = os.getenv("EBOEKHOUDEN_API_USERNAME")
    EBOEKHOUDEN_API_PASSWORD = os.getenv("EBOEKHOUDEN_API_PASSWORD")

    # Field-level encryption settings
    ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY")
    if not ENCRYPTION_KEY:
        raise ValueError("No ENCRYPTION_KEY set in environment variables")

    # Salt for key derivation (base64 encoded)
    ENCRYPTION_SALT = os.getenv("ENCRYPTION_SALT")

    # Enable/disable field-level encryption
    ENCRYPTION_ENABLED = os.getenv("ENCRYPTION_ENABLED", "True").lower() in ("true", "1", "t")