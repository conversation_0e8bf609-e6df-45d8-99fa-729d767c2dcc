#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to convert Firebase credentials from a JSON file to a single-line JSON string
for use in environment variables.
"""

import json
import sys
import os

def convert_credentials(file_path):
    """Convert Firebase credentials from a JSON file to a single-line JSON string."""
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            print(f"Error: File '{file_path}' not found.")
            return None
        
        # Read the JSON file
        with open(file_path, 'r') as f:
            credentials = json.load(f)
        
        # Convert to a single-line JSON string
        credentials_str = json.dumps(credentials)
        
        return credentials_str
    except json.JSONDecodeError:
        print(f"Error: '{file_path}' is not a valid JSON file.")
        return None
    except Exception as e:
        print(f"Error: {str(e)}")
        return None

def main():
    # Check if a file path was provided
    if len(sys.argv) < 2:
        print("Usage: python convert_firebase_credentials.py <path_to_credentials_file>")
        print("Example: python convert_firebase_credentials.py secrets/firebase-service-account-key.json")
        return
    
    file_path = sys.argv[1]
    credentials_str = convert_credentials(file_path)
    
    if credentials_str:
        print("\nFirebase credentials as a JSON string:")
        print("-------------------------------------")
        print(f"FIREBASE_CREDENTIALS_JSON={credentials_str}")
        print("-------------------------------------")
        print("\nAdd this line to your .env file to use Firebase credentials as a JSON string.")
        print("Make sure to remove or comment out the FIREBASE_CREDENTIALS_PATH line.")

if __name__ == "__main__":
    main()
