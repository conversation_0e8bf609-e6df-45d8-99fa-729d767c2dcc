"""
File validation utility module.
This module provides utilities for validating file uploads.
"""
import os
import magic
import logging
from werkzeug.utils import secure_filename

logger = logging.getLogger(__name__)

ALLOWED_EXTENSIONS = {
    'pdf': ['application/pdf'],
    'jpg': ['image/jpeg'],
    'jpeg': ['image/jpeg'],
    'png': ['image/png'],
    'docx': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/zip'],
    'doc': ['application/msword'],
    'xlsx': ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/zip'],
    'xls': ['application/vnd.ms-excel'],
}

# Maximum file size (10 MB)
MAX_FILE_SIZE = 10 * 1024 * 1024

def allowed_file(filename):
    """
    Check if the file has an allowed extension.

    Args:
        filename (str): The filename to check

    Returns:
        bool: True if the file has an allowed extension, False otherwise
    """
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_file_extension(file):
    """
    Validate the file extension.

    Args:
        file: The file object to validate

    Returns:
        tuple: (is_valid, message)
    """
    if file.filename == '':
        return False, "No selected file"

    if not allowed_file(file.filename):
        allowed_ext = ', '.join(ALLOWED_EXTENSIONS.keys())
        return False, f"File type not allowed. Allowed types: {allowed_ext}"

    return True, "File extension is valid"

def validate_file_content(file):
    """
    Validate the file content using magic numbers.

    Args:
        file: The file object to validate

    Returns:
        tuple: (is_valid, message)
    """
    # Save the current position
    current_position = file.tell()

    # Read the beginning of the file to detect MIME type
    file_content = file.read(2048)
    mime = magic.from_buffer(file_content, mime=True)

    # Reset file position
    file.seek(current_position)

    # Get the file extension
    extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

    # Check if the MIME type matches the expected MIME type for the extension
    if extension in ALLOWED_EXTENSIONS and mime not in ALLOWED_EXTENSIONS[extension]:
        return False, f"File content does not match the extension. Detected: {mime}"

    return True, "File content is valid"

def validate_file_size(file):
    """
    Validate the file size.

    Args:
        file: The file object to validate

    Returns:
        tuple: (is_valid, message)
    """
    # Save the current position
    current_position = file.tell()

    # Go to the end of the file to get its size
    file.seek(0, os.SEEK_END)
    file_size = file.tell()

    # Reset file position
    file.seek(current_position)

    if file_size > MAX_FILE_SIZE:
        return False, f"File too large. Maximum size is {MAX_FILE_SIZE / (1024 * 1024)} MB"

    return True, "File size is valid"

def validate_file(file):
    """
    Validate a file upload.

    Args:
        file: The file object to validate

    Returns:
        tuple: (is_valid, message)
    """
    # Check if file exists
    if not file:
        return False, "No file part in the request"

    # Validate file extension
    is_valid, message = validate_file_extension(file)
    if not is_valid:
        return False, message

    # Validate file size
    is_valid, message = validate_file_size(file)
    if not is_valid:
        return False, message

    # Validate file content
    is_valid, message = validate_file_content(file)
    if not is_valid:
        return False, message

    return True, "File is valid"

def get_secure_filename(filename):
    """
    Get a secure version of the filename.

    Args:
        filename (str): The original filename

    Returns:
        str: A secure version of the filename
    """
    return secure_filename(filename)
