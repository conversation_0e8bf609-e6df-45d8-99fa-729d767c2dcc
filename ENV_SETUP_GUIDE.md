# Environment Variables Setup Guide

This guide will help you configure the `.env` files for both the frontend and backend of your Customer Management System.

## 🔧 Backend Configuration (`backend/.env`)

### 1. Database Configuration
Replace the database URL with your PostgreSQL credentials:
```bash
DATABASE_URL=postgresql://your_db_user:your_db_password@localhost:5432/customer_management
```

**Example:**
```bash
DATABASE_URL=postgresql://postgres:mypassword@localhost:5432/customer_management
```

### 2. Security Keys
Generate secure keys for your application:

**SECRET_KEY:** Used for Flask session management and CSRF protection
```bash
# Generate using Python:
python -c "import secrets; print(secrets.token_hex(32))"
```

**ENCRYPTION_KEY:** Used for field-level encryption of sensitive data
```bash
# Generate using Python:
python -c "import secrets; print(secrets.token_hex(32))"
```

Replace the placeholders in your `.env` file:
```bash
SECRET_KEY=your_generated_64_character_hex_string
ENCRYPTION_KEY=your_generated_64_character_hex_string
```

### 3. Firebase Configuration
You have two options for Firebase credentials:

**Option 1: Service Account File (Recommended for Development)**
1. Go to Firebase Console → Project Settings → Service Accounts
2. Click "Generate new private key"
3. Save the JSON file as `backend/secrets/firebase-service-account-key.json`
4. Make sure the path in `.env` is correct:
```bash
FIREBASE_CREDENTIALS_PATH=./secrets/firebase-service-account-key.json
```

**Option 2: JSON String (Recommended for Production)**
1. Copy the entire JSON content from your service account file
2. Uncomment and replace the FIREBASE_CREDENTIALS_JSON line:
```bash
FIREBASE_CREDENTIALS_JSON={"type":"service_account","project_id":"your-actual-project-id",...}
```

### 4. Admin User
Replace with your actual admin email:
```bash
ADMIN_EMAIL=<EMAIL>
```

### 5. Development vs Production
For development, set:
```bash
FLASK_DEBUG=True
```

For production, set:
```bash
FLASK_DEBUG=False
```

## 🌐 Frontend Configuration (`frontend/.env`)

### 1. API URL
Make sure this matches your backend server:
```bash
VITE_API_URL=https://localhost:5000/api
```

### 2. Firebase Configuration
Get these values from Firebase Console → Project Settings → General → Your apps:

```bash
VITE_FIREBASE_API_KEY=your_actual_api_key
VITE_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-actual-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_actual_sender_id
VITE_FIREBASE_APP_ID=your_actual_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_actual_measurement_id
```

## 🚀 Quick Setup Steps

### 1. Backend Setup
```bash
cd backend
# Generate secret keys
python -c "import secrets; print('SECRET_KEY=' + secrets.token_hex(32))"
python -c "import secrets; print('ENCRYPTION_KEY=' + secrets.token_hex(32))"

# Edit .env file with the generated keys and your database credentials
# Make sure to create the secrets directory for Firebase
mkdir -p secrets
```

### 2. Frontend Setup
```bash
cd frontend
# Edit .env file with your Firebase configuration
# You can use the setup script if you have a Firebase config object:
node setup_firebase_config.js
```

### 3. Database Setup
Make sure PostgreSQL is running and create the database:
```sql
CREATE DATABASE customer_management;
```

### 4. Firebase Setup
1. Create a Firebase project at https://console.firebase.google.com
2. Enable Authentication with Email/Password
3. Generate service account credentials
4. Configure both frontend and backend with the credentials

## 🔒 Security Notes

1. **Never commit `.env` files to version control**
2. **Use strong, unique keys for production**
3. **Keep Firebase credentials secure**
4. **Use HTTPS in production**
5. **Set FLASK_DEBUG=False in production**

## 🧪 Testing Your Configuration

### Backend Test
```bash
cd backend
python -c "from app.config import Config; print('Backend config loaded successfully')"
```

### Frontend Test
```bash
cd frontend
npm run dev
# Check browser console for any Firebase configuration errors
```

## 📝 Environment File Templates

The `.env` files have been created with placeholder values. Make sure to replace all placeholder values with your actual configuration before running the application.

## 🆘 Troubleshooting

**Common Issues:**
1. **Database connection errors:** Check your PostgreSQL credentials and ensure the database exists
2. **Firebase errors:** Verify your Firebase configuration and ensure the service account has proper permissions
3. **CORS errors:** Make sure the frontend API URL matches your backend server
4. **Missing secrets:** Ensure all required environment variables are set

For more help, check the main README.md file or the project documentation.
