"""
Document Template repository module.
This module provides data access methods for the DocumentTemplate model.
"""
from typing import List, Optional, Dict
from app import db
from app.models.document_template import DocumentTemplate
import os
import logging

logger = logging.getLogger(__name__)

class DocumentTemplateRepository:
    """Repository for DocumentTemplate model."""
    
    def get_all(self) -> List[DocumentTemplate]:
        """Get all document templates."""
        return DocumentTemplate.query.all()
    
    def get_by_id(self, template_id: int) -> Optional[DocumentTemplate]:
        """Get a document template by ID."""
        return DocumentTemplate.query.get(template_id)
    
    def get_by_document_type(self, document_type: str) -> List[DocumentTemplate]:
        """Get all document templates for a specific document type."""
        return DocumentTemplate.query.filter_by(document_type=document_type).all()
    
    def create(self, template_data: Dict, file) -> DocumentTemplate:
        """Create a new document template."""
        # Create templates directory if it doesn't exist
        templates_dir = os.path.join(os.getcwd(), 'templates')
        os.makedirs(templates_dir, exist_ok=True)
        
        # Generate a unique filename
        import datetime as dt
        timestamp = int(dt.datetime.now(dt.timezone.utc).timestamp())
        filename = f"{timestamp}_{template_data['document_type']}_template.{template_data['file_type']}"
        file_path = os.path.join(templates_dir, filename)
        
        # Save the file
        file.save(file_path)
        
        # Create the template record
        template = DocumentTemplate(
            name=template_data['name'],
            document_type=template_data['document_type'],
            description=template_data.get('description'),
            file_path=file_path,
            file_type=template_data['file_type'],
            created_by=template_data['created_by']
        )
        
        db.session.add(template)
        db.session.commit()
        
        return template
    
    def update(self, template: DocumentTemplate, template_data: Dict) -> DocumentTemplate:
        """Update a document template."""
        for key, value in template_data.items():
            if key != 'file_path' and key != 'file_type':  # Don't update file path or type
                setattr(template, key, value)
        
        db.session.commit()
        return template
    
    def delete(self, template: DocumentTemplate) -> bool:
        """Delete a document template."""
        try:
            # Delete the file if it exists
            if os.path.exists(template.file_path):
                os.remove(template.file_path)
            
            db.session.delete(template)
            db.session.commit()
            return True
        except Exception as e:
            logger.error(f"Error deleting template: {str(e)}")
            db.session.rollback()
            return False
