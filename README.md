# Customer Management Application

A comprehensive customer management system built with <PERSON>lask (backend) and <PERSON><PERSON> (frontend).

## Features

- User authentication and role-based access control
- Customer management
- Document management with file uploads
- Event scheduling and tracking
- User permissions system
- Audit logging
- Export/import functionality

## Tech Stack

### Backend
- Flask (Python)
- PostgreSQL
- Firebase Authentication
- SQLAlchemy ORM

### Frontend
- React
- TypeScript
- Tailwind CSS
- Vite

## Getting Started

### Prerequisites

- Node.js (v14+)
- Python (v3.8+)
- PostgreSQL
- Firebase project with Authentication enabled

### Setup

1. Clone the repository
   ```
   git clone https://github.com/yourusername/customer_management.git
   cd customer_management
   ```

2. Set up the backend
   ```
   cd backend
   python -m venv venv
   .\venv\scripts\activate  # Windows
   source venv/bin/activate  # Linux/Mac
   pip install -r requirements.txt
   ```

3. Set up Firebase credentials (see [Firebase Setup Guide](FIREBASE_SETUP.md))

4. Create a `.env` file in the backend directory:
   ```
   # Copy the example file
   cp .env.example .env

   # Edit the file with your configuration
   # For Firebase credentials, you can use the conversion script:
   python convert_firebase_credentials.py path/to/your/firebase-credentials.json
   ```

5. Set up the frontend
   ```
   cd ../frontend
   npm install
   ```

6. Create a `.env` file in the frontend directory:
   ```
   # Copy the example file
   cp .env.example .env

   # Edit the file with your configuration
   # For Firebase configuration, you can use the setup script:
   node setup_firebase_config.js
   ```

### Running the Application

1. Start the backend server
   ```
   cd backend
   .\venv\scripts\activate  # Windows
   source venv/bin/activate  # Linux/Mac
   python run.py
   ```

2. Start the frontend development server
   ```
   cd frontend
   npm run dev
   ```

3. Access the application at `https://localhost:5173`

## Security

This application implements several security measures:

- Firebase Authentication for secure user authentication
- Role-based access control
- Token-based API security
- HTTPS for all communications
- Input validation and sanitization

For more details, see the [Security Analysis Report](security_analysis_report.md).

## License

This project is licensed under the MIT License - see the LICENSE file for details.
