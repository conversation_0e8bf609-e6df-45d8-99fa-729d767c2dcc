from PIL import Image, ImageDraw, ImageFont
import os

# Create a blank image with a white background
width, height = 400, 150
image = Image.new('RGB', (width, height), color='white')

# Get a drawing context
draw = ImageDraw.Draw(image)

# Draw a rectangle border
draw.rectangle([(0, 0), (width-1, height-1)], outline='black')

# Try to use a TrueType font, fall back to default if not available
try:
    font = ImageFont.truetype("arial.ttf", 36)
except IOError:
    font = ImageFont.load_default()

# Draw text in the center
text = "AMSPM Security"
text_width, text_height = draw.textsize(text, font=font) if hasattr(draw, 'textsize') else font.getsize(text)
position = ((width - text_width) // 2, (height - text_height) // 2)
draw.text(position, text, fill='black', font=font)

# Save the image
logo_path = os.path.join('app', 'static', 'images', 'logo.png')
image.save(logo_path)

print(f"Logo created at {logo_path}")
