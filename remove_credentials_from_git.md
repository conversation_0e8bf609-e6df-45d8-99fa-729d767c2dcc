# Removing Firebase Credentials from Git Tracking

To remove the Firebase credentials file from Git tracking without deleting the actual file, follow these steps:

1. Open a terminal or command prompt
2. Navigate to your project root directory:
   ```
   cd path/to/customer_management
   ```
3. Run the following Git command:
   ```
   git rm --cached backend/secrets/firebase-service-account-key.json
   ```
4. Commit the change:
   ```
   git commit -m "Remove Firebase credentials from Git tracking"
   ```
5. Push the changes to your repository:
   ```
   git push
   ```

This will remove the file from Git tracking but keep the actual file on your local system. Future changes to this file will be ignored by Git as long as it's properly excluded in your `.gitignore` file.

## Rotating Firebase Service Account Keys

Since the credentials were exposed in the repository, it's a good security practice to generate new service account keys:

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Click on the gear icon (⚙️) next to "Project Overview" to open Project settings
4. Go to the "Service accounts" tab
5. Click "Generate new private key" button
6. Save the downloaded JSON file securely
7. Update your application to use the new credentials:
   - If using a file: Replace the old credentials file with the new one
   - If using environment variables: Update your `.env` file with the new credentials JSON string

## Verifying the Changes

After removing the file from Git tracking, you can verify that it's no longer tracked by running:
```
git ls-files | grep firebase
```

This command should not show the Firebase credentials file in the output.
