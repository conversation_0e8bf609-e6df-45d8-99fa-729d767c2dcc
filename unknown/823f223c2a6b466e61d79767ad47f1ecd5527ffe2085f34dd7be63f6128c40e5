from app.repositories.user_repository import UserRepository
from app.services.audit_service import AuditService
from app.utils.cache_utils import clear_cache_for_entity
import firebase_admin
from firebase_admin import auth
from typing import List, Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class UserService:
    def __init__(self):
        self.user_repo = UserRepository()
        self.audit_service = AuditService()

    def get_all_users(self, page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]:
        users, total = self.user_repo.get_all(page, per_page)
        return [user.to_dict() for user in users], total

    def get_user_by_id(self, user_id: int) -> Dict:
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")
        return user.to_dict()

    def create_user(self, email: str, password: str, role: str, name: str = None, current_user_id: int = None, ip_address: str = None, user_agent: str = None) -> Dict:
        role = role.lower()
        try:
            firebase_user = auth.create_user(email=email, password=password)
        except Exception as e:
            raise Exception(f"Failed to create user in Firebase: {str(e)}")
        try:
            auth.set_custom_user_claims(firebase_user.uid, {"role": role})
            user = self.user_repo.create(firebase_user.uid, email, role, name)

            user_dict = user.to_dict()

            # Log the action
            self.audit_service.log_action(
                user_id=current_user_id,
                action='create',
                entity_type='user',
                entity_id=user.id,
                details={
                    'email': email,
                    'role': role,
                    'name': name
                },
                ip_address=ip_address,
                user_agent=user_agent
            )

            # Clear cache
            clear_cache_for_entity('user')

            return user_dict
        except Exception as e:
            auth.delete_user(firebase_user.uid)
            raise Exception(f"Failed to create user: {str(e)}")

    def update_user_role(self, user_id: int, new_role: str, current_user_id: int = None, ip_address: str = None, user_agent: str = None) -> Dict:
        new_role = new_role.lower()
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")
        user = self.user_repo.update_role(user, new_role)
        try:
            auth.set_custom_user_claims(user.firebase_uid, {"role": new_role})
        except Exception as e:
            raise Exception(f"Failed to update role in Firebase: {str(e)}")
        user_dict = user.to_dict()

        # Log the action
        self.audit_service.log_action(
            user_id=current_user_id,
            action='update_role',
            entity_type='user',
            entity_id=user_id,
            details={
                'old_role': user.role,
                'new_role': new_role,
                'email': user.email
            },
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('user', user_id)

        return user_dict

    def update_user_name(self, user_id: int, new_name: str, current_user_id: int = None, ip_address: str = None, user_agent: str = None) -> Dict:
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")
        user = self.user_repo.update_name(user, new_name)
        user_dict = user.to_dict()

        # Log the action
        self.audit_service.log_action(
            user_id=current_user_id,
            action='update_name',
            entity_type='user',
            entity_id=user_id,
            details={
                'old_name': user.name,
                'new_name': new_name,
                'email': user.email
            },
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('user', user_id)

        return user_dict

    def update_user_password(self, user_id: int, new_password: str, current_user_id: int = None, ip_address: str = None, user_agent: str = None) -> Dict:
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")

        # Only allow users to update their own password or administrators to update any password
        if current_user_id != user_id:
            current_user = self.user_repo.get_by_id(current_user_id)
            if not current_user or current_user.role != "administrator":
                raise Exception("You can only update your own password")

        try:
            # Update password in Firebase
            auth.update_user(user.firebase_uid, password=new_password)

            # Log the action (don't log the password)
            self.audit_service.log_action(
                user_id=current_user_id,
                action='update_password',
                entity_type='user',
                entity_id=user_id,
                details={
                    'email': user.email,
                },
                ip_address=ip_address,
                user_agent=user_agent
            )

            return user.to_dict()
        except Exception as e:
            raise Exception(f"Failed to update password in Firebase: {str(e)}")

    def delete_user(self, user_id: int, current_user_id: int = None, ip_address: str = None, user_agent: str = None) -> bool:
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")
        try:
            auth.delete_user(user.firebase_uid)
        except Exception as e:
            raise Exception(f"Failed to delete user in Firebase: {str(e)}")

        # Delete user permissions first
        self.permission_repo.delete_by_user_id(user_id)

        # Then delete the user
        result = self.user_repo.delete(user_id)

        # Log the action
        self.audit_service.log_action(
            user_id=current_user_id,
            action='delete',
            entity_type='user',
            entity_id=user_id,
            details={
                'email': user.email,
                'role': user.role,
                'name': user.name
            },
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('user', user_id)

        return result

