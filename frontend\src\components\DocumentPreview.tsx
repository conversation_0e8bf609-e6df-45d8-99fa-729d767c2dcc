import React, { useState, useEffect } from 'react';
import api from '../api';
import LoadingSpinner from './LoadingSpinner';
import { auth } from '../firebase';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import { FaSearchMinus, FaSearchPlus, FaUndo } from 'react-icons/fa';

// Set up the worker for PDF.js
// Use HTTPS for the worker to avoid mixed content issues
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

interface DocumentPreviewProps {
  documentUrl: string;
  documentId?: number;  // Add document ID for using the new endpoint
  onClose: () => void;
}

const DocumentPreview: React.FC<DocumentPreviewProps> = ({ documentUrl, documentId, onClose }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isMockEnvironment, setIsMockEnvironment] = useState(false);
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.2);
  const [authToken, setAuthToken] = useState<string | null>(null);

  // Get authentication token
  useEffect(() => {
    const getToken = async () => {
      try {
        const user = auth.currentUser;
        if (user) {
          const token = await user.getIdToken();
          setAuthToken(token);
        }
      } catch (error) {
        console.error('Error getting auth token:', error);
      }
    };

    getToken();
  }, []);

  useEffect(() => {
    const fetchDocument = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check if this is a mock storage URL
        if (documentUrl.includes('mock-storage')) {
          console.log('Mock storage detected, using direct URL approach');
          setIsMockEnvironment(true);
          // In mock environment, we'll just use the URL directly without fetching
          // This won't work for preview due to CORS, but we'll show a message
          setPreviewUrl(documentUrl);
          setLoading(false);
          return;
        }

        // Determine which URL to use
        let fetchUrl;

        // If we have a document ID, use the new endpoint
        if (documentId) {
          // Use the correct URL pattern: /documents/{id}/file
          fetchUrl = `/documents/${documentId}/file`;
          console.log(`Using document ID endpoint: ${fetchUrl}`);
        }
        // If the URL is encrypted (contains base64-like characters), use the document ID endpoint instead
        else if (documentUrl.includes('=') && documentUrl.length > 100) {
          console.log('URL appears to be encrypted, cannot use directly');
          setError('Cannot display document: URL is encrypted. Please use document ID instead.');
          setLoading(false);
          return;
        }
        // Otherwise use the direct URL
        else {
          fetchUrl = documentUrl;
          console.log(`Using direct URL: ${fetchUrl}`);
        }

        // For real environments, fetch the document as a blob
        // Make sure we're using the full URL with authentication
        const authUrl = fetchUrl.startsWith('/')
          ? `${fetchUrl}${fetchUrl.includes('?') ? '&' : '?'}token=${authToken || ''}`
          : fetchUrl;

        const response = await api.get(authUrl, {
          responseType: 'blob',
        });

        // Create a blob URL for the document
        const blob = new Blob([response.data], { type: response.headers['content-type'] || 'application/pdf' });
        const url = URL.createObjectURL(blob);
        setPreviewUrl(url);
      } catch (err: any) {
        console.error('Error fetching document:', err);

        // Check if this is a CORS error
        if (err.message === 'Network Error' && documentUrl.includes('mock-storage')) {
          setIsMockEnvironment(true);
          setError('Cannot preview mock documents due to CORS restrictions');
        } else {
          setError(err.message || 'Failed to load document');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchDocument();

    // Cleanup function to revoke the blob URL when the component unmounts
    return () => {
      if (previewUrl && !previewUrl.includes('mock-storage')) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [documentUrl, documentId, authToken]);

  // Extract filename from URL for display
  const getFilenameFromUrl = (url: string) => {
    try {
      const urlParts = url.split('/');
      let filename = urlParts[urlParts.length - 1];
      // Decode URL-encoded characters
      filename = decodeURIComponent(filename);
      return filename;
    } catch (e) {
      return 'document';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold text-amspm-text">Document Preview</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="flex-1 overflow-hidden p-4">
          {loading ? (
            <div className="h-full flex items-center justify-center">
              <LoadingSpinner message="Loading document..." fullScreen={false} />
            </div>
          ) : error ? (
            <div className="h-full flex flex-col items-center justify-center">
              <div className="text-red-500 text-center mb-4">
                <p className="mb-2">Error loading document:</p>
                <p>{error}</p>
              </div>

              {isMockEnvironment && (
                <div className="text-center mt-4">
                  <p className="mb-4">This is a mock environment. Document preview is not available due to CORS restrictions.</p>
                  <p className="mb-2">Document filename:</p>
                  <p className="font-medium">{getFilenameFromUrl(documentUrl)}</p>
                  <div className="mt-4">
                    <a
                      href={documentUrl}
                      target="_blank"
                      rel="noreferrer"
                      className="btn btn-primary"
                      onClick={(e) => {
                        e.preventDefault();
                        alert('In a real environment, this would download the document.');
                      }}
                    >
                      Attempt Download
                    </a>
                  </div>
                </div>
              )}
            </div>
          ) : isMockEnvironment ? (
            <div className="h-full flex flex-col items-center justify-center">
              <div className="text-center mb-4">
                <p className="mb-4">This is a mock environment. Document preview is not available due to CORS restrictions.</p>
                <p className="mb-2">Document filename:</p>
                <p className="font-medium">{getFilenameFromUrl(documentUrl)}</p>
                <div className="mt-4">
                  <a
                    href={documentUrl}
                    target="_blank"
                    rel="noreferrer"
                    className="btn btn-primary"
                    onClick={(e) => {
                      e.preventDefault();
                      alert('In a real environment, this would download the document.');
                    }}
                  >
                    Attempt Download
                  </a>
                </div>
              </div>
            </div>
          ) : (
            <div className="h-full">
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center">
                  {numPages && (
                    <span className="text-sm text-gray-600">
                      Page {pageNumber} of {numPages}
                    </span>
                  )}
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setScale(prevScale => Math.max(prevScale - 0.2, 0.6))}
                    className="btn btn-sm btn-outline"
                    title="Zoom Out"
                  >
                    <FaSearchMinus />
                  </button>
                  <button
                    onClick={() => setScale(1.2)}
                    className="btn btn-sm btn-outline"
                    title="Reset Zoom"
                  >
                    <FaUndo />
                  </button>
                  <button
                    onClick={() => setScale(prevScale => Math.min(prevScale + 0.2, 3))}
                    className="btn btn-sm btn-outline"
                    title="Zoom In"
                  >
                    <FaSearchPlus />
                  </button>
                </div>
              </div>

              <div className="border border-gray-300 rounded overflow-auto" style={{ height: 'calc(100% - 40px)' }}>
                <Document
                  file={previewUrl || ''}
                  onLoadSuccess={({ numPages }) => {
                    setNumPages(numPages);
                  }}
                  options={{
                    // Add authentication to PDF.js requests
                    httpHeaders: {
                      'Authorization': authToken ? `Bearer ${authToken}` : ''
                    },
                    cMapUrl: 'https://unpkg.com/pdfjs-dist@3.4.120/cmaps/',
                    cMapPacked: true,
                    standardFontDataUrl: 'https://unpkg.com/pdfjs-dist@3.4.120/standard_fonts/'
                  }}
                  onLoadError={(error) => {
                    console.error('Error loading PDF:', error);
                    setError(`Failed to load PDF document: ${error.message}`);
                  }}
                  loading={
                    <div className="flex justify-center items-center h-full">
                      <div className="text-center">
                        <LoadingSpinner message="Loading PDF..." fullScreen={false} />
                        <p className="text-xs text-gray-500 mt-2">URL: {documentUrl.substring(0, 50)}...</p>
                      </div>
                    </div>
                  }
                >
                  <Page
                    pageNumber={pageNumber}
                    scale={scale}
                    renderAnnotationLayer={true}
                    renderTextLayer={true}
                  />
                </Document>
              </div>

              {numPages && numPages > 1 && (
                <div className="flex justify-between items-center mt-4">
                  <button
                    onClick={() => setPageNumber(prev => Math.max(prev - 1, 1))}
                    disabled={pageNumber <= 1}
                    className={`btn btn-sm ${pageNumber <= 1 ? 'btn-disabled' : 'btn-primary'}`}
                  >
                    Previous
                  </button>
                  <p className="text-sm">
                    Page {pageNumber} of {numPages}
                  </p>
                  <button
                    onClick={() => setPageNumber(prev => Math.min(prev + 1, numPages || 1))}
                    disabled={pageNumber >= (numPages || 1)}
                    className={`btn btn-sm ${pageNumber >= (numPages || 1) ? 'btn-disabled' : 'btn-primary'}`}
                  >
                    Next
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="p-4 border-t flex justify-end">
          <button
            onClick={onClose}
            className="btn btn-outline"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default DocumentPreview;
