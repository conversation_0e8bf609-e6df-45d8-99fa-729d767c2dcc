"""
Simple script to decrypt customer data in the database.

This script:
1. Retrieves all customers from the database
2. Decrypts all encrypted fields
3. Updates the database with the decrypted data

Usage:
    python migrations/simple_decrypt_customers.py
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

def decrypt_customers():
    """Decrypt all customer data in the database"""
    from app import create_app, db
    from app.utils.encryption import decrypt
    
    app, _ = create_app()
    
    with app.app_context():
        from app.models.customer import Customer
        
        # Get all customers
        customers = Customer.query.all()
        logger.info(f"Found {len(customers)} customers to decrypt")
        
        # Fields that might be encrypted
        encrypted_fields = [
            'address', 'postal_code', 'city',
            'address2', 'postal_code2', 'city2',
            'phone', 'mobile', 'fax', 'email',
            'invoice_email', 'reminder_email',
            'bank_account', 'giro_account', 'vat_number',
            'iban', 'bic'
        ]
        
        decrypted_count = 0
        
        # Process each customer
        for customer in customers:
            for field in encrypted_fields:
                value = getattr(customer, field)
                if value and isinstance(value, str) and len(value) > 20 and '=' in value:
                    # This looks like it might be encrypted, try to decrypt it
                    try:
                        decrypted_value = decrypt(value)
                        setattr(customer, field, decrypted_value)
                        decrypted_count += 1
                        logger.info(f"Decrypted {field} for customer {customer.id}")
                    except Exception as e:
                        logger.error(f"Error decrypting {field} for customer {customer.id}: {str(e)}")
        
        # Save changes to database
        if decrypted_count > 0:
            try:
                db.session.commit()
                logger.info(f"Successfully decrypted {decrypted_count} fields")
            except Exception as e:
                db.session.rollback()
                logger.error(f"Error saving changes to database: {str(e)}")
        else:
            logger.info("No encrypted fields found")

if __name__ == "__main__":
    logger.info("Starting customer data decryption")
    decrypt_customers()
    logger.info("Decryption completed")
