# SQL Injection Security Guide

## Overview

This guide documents the SQL injection vulnerabilities that were identified and fixed in the Customer Management application, as well as best practices to prevent future SQL injection vulnerabilities.

## Fixed Vulnerabilities

### 1. CustomerRepository.search_by_name Method

**Issue**: The method was using string formatting (f-string) to construct a SQL query, which could allow an attacker to inject malicious SQL code.

**Vulnerable Code**:
```python
def search_by_name(self, search_term: str) -> List[Customer]:
    # Use ILIKE for case-insensitive search (PostgreSQL specific)
    # For other databases, you might need to use LOWER() or equivalent
    return Customer.query.filter(Customer.name.ilike(f'%{search_term}%')).all()
```

**Fixed Code**:
```python
def search_by_name(self, search_term: str) -> List[Customer]:
    # Use ILIKE for case-insensitive search (PostgreSQL specific)
    # For other databases, you might need to use LOWER() or equivalent
    # Use parameterized query to prevent SQL injection
    return Customer.query.filter(Customer.name.ilike("%" + search_term + "%")).all()
```

## Safe SQL Practices in the Codebase

### 1. SQLAlchemy ORM

Most of the database operations in the application use SQLAlchemy's ORM, which automatically handles parameterization and prevents SQL injection. Examples:

```python
# Safe: Using SQLAlchemy ORM methods
User.query.filter_by(firebase_uid=firebase_uid).first()
Customer.query.filter_by(customer_id=customer_id).all()
```

### 2. Migration Scripts

Migration scripts use raw SQL with the `text()` function but don't incorporate user input:

```python
db.session.execute(text("ALTER TABLE sessions ALTER COLUMN token TYPE TEXT"))
```

While these are not vulnerable to SQL injection (as they don't use user input), it's still good practice to use parameterized queries where possible.

## Best Practices for Preventing SQL Injection

1. **Use SQLAlchemy ORM**: Continue using SQLAlchemy's ORM methods which automatically handle parameterization.

2. **Avoid String Formatting in SQL**: Never use string formatting (f-strings, `.format()`, `%` operator) to construct SQL queries.

3. **Use Parameterized Queries**: When writing raw SQL, always use parameterized queries:

   ```python
   # Instead of this (vulnerable):
   db.session.execute(f"SELECT * FROM users WHERE name = '{name}'")
   
   # Do this (safe):
   db.session.execute(text("SELECT * FROM users WHERE name = :name"), {"name": name})
   ```

4. **Input Validation**: Validate and sanitize all user inputs before using them in database operations.

5. **Use SQLAlchemy's bindparam**: For complex queries, use SQLAlchemy's `bindparam`:

   ```python
   from sqlalchemy.sql import bindparam
   stmt = text("SELECT * FROM users WHERE name = :name")
   stmt = stmt.bindparams(bindparam("name"))
   result = db.session.execute(stmt, {"name": user_input})
   ```

6. **Limit Database Permissions**: Ensure the database user used by the application has only the necessary permissions.

7. **Regular Security Audits**: Regularly review code for potential SQL injection vulnerabilities.

## Testing for SQL Injection

Consider adding tests specifically designed to detect SQL injection vulnerabilities:

```python
def test_search_by_name_sql_injection():
    # Test with a malicious input that attempts SQL injection
    malicious_input = "' OR '1'='1"
    result = customer_repository.search_by_name(malicious_input)
    # Verify that the query returns only customers that actually match the search term
    assert all("' OR '1'='1" in customer.name for customer in result)
```

## Additional Resources

- [OWASP SQL Injection Prevention Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html)
- [SQLAlchemy Documentation on SQL Expression Language](https://docs.sqlalchemy.org/en/14/core/tutorial.html)
- [Flask-SQLAlchemy Documentation](https://flask-sqlalchemy.palletsprojects.com/en/2.x/)
