# Frontend Environment Variables for Vite
# All variables must be prefixed with VITE_ to be accessible in the frontend

# API Configuration
# Backend API URL - make sure this matches your backend server
VITE_API_URL=https://localhost:5000/api

# Firebase Configuration
# Replace these with your actual Firebase project configuration
# You can find these values in your Firebase project settings > General > Your apps
VITE_FIREBASE_API_KEY=AIzaSyC_q-4-y9QEJ33H-37B_6UyIoR7mjV1FP4
VITE_FIREBASE_AUTH_DOMAIN=amspm-a8616.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=amspm-a8616
VITE_FIREBASE_STORAGE_BUCKET=amspm-a8616.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=939633695758
VITE_FIREBASE_APP_ID=1:939633695758:web:68b20571d28c20fe9a305f
VITE_FIREBASE_MEASUREMENT_ID=G-M2Z5LPDN5Z

# Feature Flags
VITE_ENABLE_MOCK_STORAGE=true

# Development Settings
NODE_ENV=development

# Optional: PWA Settings (uncomment if using PWA features)
# VITE_PWA_NAME=Customer Management System
# VITE_PWA_SHORT_NAME=CMS
# VITE_PWA_DESCRIPTION=Customer Management System for AMSPM

# Optional: Analytics (uncomment if using analytics)
# VITE_ANALYTICS_ID=your_analytics_id
