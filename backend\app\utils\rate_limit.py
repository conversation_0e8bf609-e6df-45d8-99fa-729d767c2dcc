from functools import wraps
from app import limiter
import logging

logger = logging.getLogger(__name__)

def rate_limit(limit_string):
    """
    Decorator to apply rate limiting to a view function.

    Args:
        limit_string: A string representing the rate limit, e.g., "5/minute", "100/day"
    """
    def decorator(f):
        @wraps(f)
        @limiter.limit(limit_string, override_defaults=True)
        def decorated_function(*args, **kwargs):
            return f(*args, **kwargs)

        logger.info(f"Rate limit of {limit_string} applied to {f.__name__}")
        return decorated_function
    return decorator
