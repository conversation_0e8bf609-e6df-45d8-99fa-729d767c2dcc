# Mock flag to bypass Firebase Storage
USE_MOCK_FIREBASE = True  # Ensure this is True

def upload_file_to_storage(file, destination_path: str) -> str:
    if USE_MOCK_FIREBASE:
        print(f"[Mock] Uploading file to {destination_path}")
        return f"https://mock-storage.googleapis.com/{destination_path}"
    else:
        from firebase_admin import storage
        import uuid
        import os

        # Generate a random UUID for the filename to prevent guessing
        file_extension = os.path.splitext(destination_path)[1]
        random_filename = f"{uuid.uuid4()}{file_extension}"

        # Keep the directory structure but use random filename
        directory = os.path.dirname(destination_path)
        secure_path = f"{directory}/{random_filename}" if directory else random_filename

        bucket = storage.bucket()
        blob = bucket.blob(secure_path)
        blob.upload_from_file(file)

        # Generate a signed URL that expires after 1 hour instead of making public
        # This ensures only authenticated users with the URL can access the file
        signed_url = blob.generate_signed_url(
            version="v4",
            expiration=3600,  # 1 hour in seconds
            method="GET"
        )

        return signed_url

def delete_file_from_storage(file_path: str) -> None:
    if USE_MOCK_FIREBASE:
        print(f"[Mock] Deleting file at {file_path}")
        return
    else:
        from firebase_admin import storage
        bucket = storage.bucket()
        blob = bucket.blob(file_path)
        blob.delete()