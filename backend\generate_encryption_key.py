"""
Generate a strong encryption key for field-level encryption.

This script generates a random 32-byte key (256 bits) and encodes it as base64.
The key can be used for the ENCRYPTION_KEY environment variable.

Usage:
    python generate_encryption_key.py
"""

import os
import base64

def generate_key():
    """Generate a random 32-byte key and encode it as base64."""
    # Generate a random 32-byte key (256 bits)
    key = os.urandom(32)
    # Encode as base64 for storage
    return base64.b64encode(key).decode('utf-8')

def generate_salt():
    """Generate a random 16-byte salt and encode it as base64."""
    # Generate a random 16-byte salt
    salt = os.urandom(16)
    # Encode as base64 for storage
    return base64.b64encode(salt).decode('utf-8')

if __name__ == "__main__":
    key = generate_key()
    salt = generate_salt()
    
    print("\n=== Encryption Key and Salt ===\n")
    print(f"ENCRYPTION_KEY={key}")
    print(f"ENCRYPTION_SALT={salt}")
    print("\nAdd these to your .env file to enable field-level encryption.")
    print("Keep these values secure and make sure to back them up safely.")
    print("If you lose the encryption key, you will not be able to decrypt the data.")
