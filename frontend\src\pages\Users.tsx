import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { getAllUsers, createUser, updateUserRole, updateUserName, deleteUser } from "../services/userService";
import { User } from "../types/user";
import LoadingSpinner from '../components/LoadingSpinner';
import Pagination from '../components/Pagination';
import { useConfirmation } from '../context/ConfirmationContext';
import { useAuth } from '../context/AuthContext';
import { FaUsers, FaPlus, FaTrash, FaLock, FaEdit, FaSave, FaTimes, FaUserCog } from "react-icons/fa";
import { userCreateSchema, userUpdateSchema, validateData } from "../utils/validation";

const Users: React.FC = () => {
  const { showConfirmation } = useConfirmation();
  const { user: currentUser } = useAuth();
  const navigate = useNavigate();
  const [users, setUsers] = useState<User[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [totalItems, setTotalItems] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [newUser, setNewUser] = useState({ email: "", password: "", confirmPassword: "", name: "", role: "verkoper" });
  const [formModified, setFormModified] = useState(false);
  const initialUserRef = useRef({ email: "", password: "", confirmPassword: "", name: "", role: "verkoper" });
  const [editingNames, setEditingNames] = useState<Record<number, string>>({});
  const [originalNames, setOriginalNames] = useState<Record<number, string>>({});
  const [editingUserIds, setEditingUserIds] = useState<number[]>([]);

  const fetchUsers = async (page: number, perPage: number) => {
    try {
      const response = await getAllUsers(page, perPage);
      setUsers(response.users);
      setTotalItems(response.total);
      setLoading(false);
    } catch (err: any) {
      setError("Failed to fetch users");
      setLoading(false);
    }
  };

  // Initialize editing names when users are loaded
  useEffect(() => {
    const initialEditingNames: Record<number, string> = {};
    const initialOriginalNames: Record<number, string> = {};
    users.forEach(user => {
      initialEditingNames[user.id] = user.name || '';
      initialOriginalNames[user.id] = user.name || '';
    });
    setEditingNames(initialEditingNames);
    setOriginalNames(initialOriginalNames);
  }, [users]);

  // Track form modifications
  useEffect(() => {
    const hasChanges = JSON.stringify(newUser) !== JSON.stringify(initialUserRef.current);
    setFormModified(hasChanges);
  }, [newUser]);

  useEffect(() => {
    fetchUsers(currentPage, itemsPerPage);
  }, [currentPage, itemsPerPage]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (perPage: number) => {
    setItemsPerPage(perPage);
    setCurrentPage(1);
  };

  if (loading) {
    return <LoadingSpinner message="Loading users..." />;
  }

  // Reset form to initial state
  const resetForm = () => {
    const initialState = { email: "", password: "", confirmPassword: "", name: "", role: "verkoper" };
    setNewUser(initialState);
    setValidationErrors([]);
    setFormModified(false);
  };

  // Handle modal close with confirmation if needed
  const handleCloseModal = () => {
    if (formModified) {
      showConfirmation({
        title: "Wijzigingen negeren",
        message: "U heeft niet-opgeslagen wijzigingen. Weet u zeker dat u dit venster wilt sluiten?",
        confirmText: "Negeren",
        cancelText: "Annuleren",
        confirmButtonClass: "bg-red-600 hover:bg-red-700",
        onConfirm: () => {
          resetForm();
          setShowModal(false);
        }
      });
    } else {
      resetForm();
      setShowModal(false);
    }
  };

  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault();
    setValidationErrors([]);

    // Validate input data
    const { isValid, errors } = await validateData(userCreateSchema, newUser);

    if (!isValid) {
      setValidationErrors(errors);
      return;
    }

    try {
      const user = await createUser(newUser.email, newUser.password, newUser.role, newUser.name || undefined);
      setUsers([...users, user]);
      resetForm();
      setShowModal(false);
    } catch (err) {
      setError("Failed to create user");
      console.error(err);
    }
  };

  const handleUpdateRole = async (userId: number, newRole: string, oldRole: string) => {
    if (newRole === oldRole) return; // No change needed
    setValidationErrors([]);

    // Validate role
    const { isValid, errors } = await validateData(userUpdateSchema, { role: newRole });

    if (!isValid) {
      setValidationErrors(errors);
      return;
    }

    const user = users.find(u => u.id === userId);
    if (!user) return;

    // Check if the user is trying to change their own role
    if (currentUser && user.email === currentUser.email) {
      showConfirmation({
        title: "Cannot Change Own Role",
        message: "For security reasons, you cannot change your own role. This prevents administrators from accidentally locking themselves out.",
        confirmText: "OK",
        confirmButtonClass: "bg-blue-600 hover:bg-blue-700",
        onConfirm: () => {}
      });
      return;
    }

    showConfirmation({
      title: "Change User Role",
      message: `Are you sure you want to change ${user.name || user.email}'s role from ${oldRole} to ${newRole}?`,
      confirmText: "Change Role",
      confirmButtonClass: "bg-blue-600 hover:bg-blue-700",
      onConfirm: async () => {
        try {
          const updatedUser = await updateUserRole(userId, newRole);
          setUsers(users.map((u) => (u.id === userId ? updatedUser : u)));
        } catch (err) {
          setError("Failed to update user role");
          console.error(err);
        }
      }
    });
  };

  const handleNameChange = (userId: number, name: string) => {
    setEditingNames(prev => ({
      ...prev,
      [userId]: name
    }));
  };

  const startEditing = (userId: number) => {
    // Only allow editing one user at a time
    setEditingUserIds([userId]);
  };

  const cancelEditing = (userId: number) => {
    // Revert to original value
    setEditingNames(prev => ({
      ...prev,
      [userId]: originalNames[userId] || ''
    }));

    // Remove from editing state
    setEditingUserIds(editingUserIds.filter(id => id !== userId));
  };

  const handleSaveName = async (userId: number) => {
    try {
      const name = editingNames[userId];
      if (name === undefined) return;

      // Validate name
      const { isValid, errors } = await validateData(userUpdateSchema, { name });

      if (!isValid) {
        setValidationErrors(errors);
        return;
      }

      const updatedUser = await updateUserName(userId, name);
      setUsers(users.map((u) => (u.id === userId ? updatedUser : u)));

      // Update original name after successful update
      setOriginalNames(prev => ({
        ...prev,
        [userId]: name
      }));

      // Remove from editing state
      setEditingUserIds(editingUserIds.filter(id => id !== userId));
      setValidationErrors([]);
    } catch (err) {
      setError("Failed to update user name");
      console.error(err);
    }
  };

  // Check if there are changes to the name
  const hasNameChanges = (userId: number) => {
    return editingNames[userId] !== originalNames[userId];
  };

  // Check if a user is currently being edited
  const isEditing = (userId: number) => {
    return editingUserIds.includes(userId);
  };

  const handleManagePermissions = (userId: number) => {
    navigate(`/users/${userId}/permissions`);
  };

  const handleDelete = async (userId: number) => {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    // Prevent users from deleting their own account
    if (currentUser && user.email === currentUser.email) {
      showConfirmation({
        title: "Cannot Delete Own Account",
        message: "For security reasons, you cannot delete your own account. Please ask another administrator to do this if necessary.",
        confirmText: "OK",
        confirmButtonClass: "bg-blue-600 hover:bg-blue-700",
        onConfirm: () => {}
      });
      return;
    }

    showConfirmation({
      title: "Delete User",
      message: `Are you sure you want to delete ${user.name || user.email}? This action cannot be undone.`,
      confirmText: "Delete",
      confirmButtonClass: "bg-red-600 hover:bg-red-700",
      onConfirm: async () => {
        try {
          await deleteUser(userId);
          setUsers(users.filter((u) => u.id !== userId));
        } catch (err) {
          setError("Failed to delete user");
          console.error(err);
        }
      }
    });
  };

  const getRoleBadgeClass = (role: string) => {
    switch (role) {
      case "administrator":
        return "bg-red-100 text-red-800 border border-red-200";
      case "verkoper":
        return "bg-blue-100 text-blue-800 border border-blue-200";
      case "monteur":
        return "bg-green-100 text-green-800 border border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border border-gray-200";
    }
  };

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-8 space-y-3 sm:space-y-0">
        <div className="flex items-center">
          <FaUsers className="text-amspm-primary mr-2 sm:mr-3 hidden sm:block" size={30} />
          <FaUsers className="text-amspm-primary mr-2 sm:hidden" size={24} />
          <h1 className="text-2xl sm:text-3xl font-bold text-amspm-text">Gebruikersbeheer</h1>
        </div>
        <button
          onClick={() => setShowModal(true)}
          className="btn btn-secondary flex items-center text-sm sm:text-base w-full sm:w-auto"
        >
          <FaPlus className="mr-2" /> Gebruiker Aanmaken
        </button>
      </div>

      {error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">{error}</div>}

      {validationErrors.length > 0 && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          <ul className="list-disc pl-5">
            {validationErrors.map((err, index) => (
              <li key={index}>{err}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Create User Modal */}
      {showModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={handleCloseModal}
        >
          <div
            className="bg-white dark:bg-dark-secondary rounded-lg shadow-lg p-4 sm:p-6 w-full max-w-md"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text">Nieuwe Gebruiker Aanmaken</h2>
              <button
                onClick={handleCloseModal}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              >
                &times;
              </button>
            </div>

            {validationErrors.length > 0 && (
              <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4 rounded shadow-sm">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">Corrigeer de volgende fouten:</h3>
                    <ul className="mt-2 text-sm text-yellow-700 list-disc list-inside">
                      {validationErrors.map((err, index) => (
                        <li key={index}>{err}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            <form onSubmit={handleCreate} className="space-y-4">
              <div className="form-group">
                <label className="block text-amspm-text dark:text-dark-text font-medium mb-1">
                  E-mail <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                  className={`input ${
                    newUser.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newUser.email)
                      ? "border-red-500 focus:ring-red-500"
                      : ""
                  }`}
                  required
                />
                {newUser.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newUser.email) && (
                  <p className="text-red-500 text-xs mt-1">Voer een geldig e-mailadres in</p>
                )}
              </div>

              <div className="form-group">
                <label className="block text-amspm-text dark:text-dark-text font-medium mb-1">Naam</label>
                <input
                  type="text"
                  value={newUser.name}
                  onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                  className="input"
                  placeholder="Volledige naam (optioneel)"
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-text dark:text-dark-text font-medium mb-1">
                  Wachtwoord <span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  value={newUser.password}
                  onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                  className="input"
                  required
                />
                <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <p>Wachtwoord moet minimaal:</p>
                  <ul className="list-disc pl-5 mt-1">
                    <li className={newUser.password.length >= 8 ? "text-green-600 dark:text-green-400" : ""}>8 tekens lang zijn</li>
                    <li className={/[A-Z]/.test(newUser.password) ? "text-green-600 dark:text-green-400" : ""}>Eén hoofdletter bevatten</li>
                    <li className={/[a-z]/.test(newUser.password) ? "text-green-600 dark:text-green-400" : ""}>Eén kleine letter bevatten</li>
                    <li className={/[0-9]/.test(newUser.password) ? "text-green-600 dark:text-green-400" : ""}>Eén cijfer bevatten</li>
                    <li className={/[@$!%*?&]/.test(newUser.password) ? "text-green-600 dark:text-green-400" : ""}>Eén speciaal teken bevatten (@$!%*?&)</li>
                  </ul>
                </div>
              </div>

              <div className="form-group">
                <label className="block text-amspm-text dark:text-dark-text font-medium mb-1">
                  Wachtwoord bevestigen <span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  value={newUser.confirmPassword}
                  onChange={(e) => setNewUser({ ...newUser, confirmPassword: e.target.value })}
                  className={`input ${
                    newUser.confirmPassword && newUser.password !== newUser.confirmPassword
                      ? "border-red-500 focus:ring-red-500"
                      : ""
                  }`}
                  required
                />
                {newUser.confirmPassword && newUser.password !== newUser.confirmPassword && (
                  <p className="text-red-500 text-xs mt-1">Wachtwoorden komen niet overeen</p>
                )}
                {newUser.confirmPassword && newUser.password === newUser.confirmPassword && (
                  <p className="text-green-600 dark:text-green-400 text-xs mt-1">Wachtwoorden komen overeen</p>
                )}
              </div>

              <div className="form-group">
                <label className="block text-amspm-text dark:text-dark-text font-medium mb-1">
                  Rol <span className="text-red-500">*</span>
                </label>
                <select
                  value={newUser.role}
                  onChange={(e) => setNewUser({ ...newUser, role: e.target.value })}
                  className="input"
                >
                  <option value="administrator">Administrator</option>
                  <option value="verkoper">Verkoper</option>
                  <option value="monteur">Monteur</option>
                </select>
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <button
                  type="button"
                  onClick={handleCloseModal}
                  className="btn btn-outline"
                >
                  Annuleren
                </button>
                <button type="submit" className="btn btn-secondary">Gebruiker Aanmaken</button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Users Table - Desktop */}
      <div className="hidden md:block bg-white rounded-lg shadow overflow-hidden overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {users
              // Sort users to put the current user at the top
              .sort((a, b) => {
                if (currentUser && a.email === currentUser.email) return -1;
                if (currentUser && b.email === currentUser.email) return 1;
                return 0;
              })
              .map((user) => (
              <tr key={user.id} className={`hover:bg-gray-50 ${currentUser && user.email === currentUser.email ? 'bg-blue-50' : ''}`}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10 rounded-full bg-amspm-primary text-white flex items-center justify-center">
                      {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
                    </div>
                    <div className="ml-4 flex-grow">
                      <div className="flex items-center space-x-2">
                        <div className="w-full">
                          {isEditing(user.id) ? (
                            <input
                              type="text"
                              value={editingNames[user.id] !== undefined ? editingNames[user.id] : user.name || ''}
                              onChange={(e) => handleNameChange(user.id, e.target.value)}
                              placeholder="Enter name"
                              className={`text-sm border rounded p-1 w-full ${currentUser && user.email === currentUser.email ? 'border-amspm-primary' : 'border-gray-300'}`}
                              autoFocus
                            />
                          ) : (
                            <span className="text-sm">{user.name || 'No name set'}</span>
                          )}
                        </div>
                      </div>
                      <div className="text-sm text-gray-500">
                        {user.email}
                        {currentUser && user.email === currentUser.email && (
                          <span className="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">You</span>
                        )}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {isEditing(user.id) && !(currentUser && user.email === currentUser.email) ? (
                      <select
                        value={user.role}
                        onChange={(e) => handleUpdateRole(user.id, e.target.value, user.role)}
                        className="text-sm border border-gray-300 rounded p-1 bg-white text-amspm-text focus:border-amspm-primary focus:ring focus:ring-amspm-primary focus:ring-opacity-50 w-full"
                      >
                        <option value="administrator">Administrator</option>
                        <option value="verkoper">Verkoper</option>
                        <option value="monteur">Monteur</option>
                      </select>
                    ) : (
                      <div className="flex items-center">
                        <span className={`px-2 py-1 text-xs rounded-full capitalize ${getRoleBadgeClass(user.role)}`}>
                          {user.role}
                        </span>
                        {currentUser && user.email === currentUser.email && (
                          <div className="ml-2 flex items-center text-gray-500" title="You cannot change your own role">
                            <FaLock className="mr-1" size={12} />
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-2">
                    {isEditing(user.id) ? (
                      <>
                        <button
                          onClick={() => handleSaveName(user.id)}
                          className="text-green-600 hover:text-green-900 disabled:text-gray-400 disabled:cursor-not-allowed"
                          disabled={!hasNameChanges(user.id)}
                          title="Save changes"
                        >
                          <FaSave />
                        </button>
                        <button
                          onClick={() => cancelEditing(user.id)}
                          className="text-gray-600 hover:text-gray-900"
                          title="Cancel editing"
                        >
                          <FaTimes />
                        </button>
                      </>
                    ) : (
                      <>
                        <button
                          onClick={() => startEditing(user.id)}
                          className="text-blue-600 hover:text-blue-900"
                          title="Edit user"
                        >
                          <FaEdit />
                        </button>
                        {user.role !== "administrator" && (
                          <button
                            onClick={() => handleManagePermissions(user.id)}
                            className="text-purple-600 hover:text-purple-900"
                            title="Manage permissions"
                          >
                            <FaUserCog />
                          </button>
                        )}
                      </>
                    )}
                    <button
                      onClick={() => handleDelete(user.id)}
                      className={`${currentUser && user.email === currentUser.email ? 'text-gray-400 cursor-not-allowed' : 'text-red-600 hover:text-red-900'}`}
                      title={currentUser && user.email === currentUser.email ? "You cannot delete your own account" : "Delete user"}
                      disabled={currentUser && user.email === currentUser.email ? true : false}
                    >
                      <FaTrash />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Users Cards - Mobile */}
      <div className="md:hidden space-y-4">
        {users
          // Sort users to put the current user at the top
          .sort((a, b) => {
            if (currentUser && a.email === currentUser.email) return -1;
            if (currentUser && b.email === currentUser.email) return 1;
            return 0;
          })
          .map((user) => (
          <div key={user.id} className={`rounded-lg shadow p-4 ${currentUser && user.email === currentUser.email ? 'bg-blue-50 border border-blue-200' : 'bg-white'}`}>
            <div className="flex items-center mb-3">
              <div className="flex-shrink-0 h-12 w-12 rounded-full bg-amspm-primary text-white flex items-center justify-center text-lg">
                {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
              </div>
              <div className="ml-4">
                <div className="font-medium text-amspm-primary">
                  {user.name || user.email}
                  {currentUser && user.email === currentUser.email && (
                    <span className="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">You</span>
                  )}
                </div>
                <div className="text-xs text-gray-500">{user.email}</div>
              </div>
            </div>

            <div className="mb-3">
              <div className="flex items-center justify-between mb-1">
                <p className="text-sm text-gray-500 font-medium">Name:</p>
              </div>
              <div className="flex items-center justify-between mb-3">
                <div className="flex-grow">
                  {isEditing(user.id) ? (
                    <input
                      type="text"
                      value={editingNames[user.id] !== undefined ? editingNames[user.id] : user.name || ''}
                      onChange={(e) => handleNameChange(user.id, e.target.value)}
                      placeholder="Enter user's name"
                      className={`w-full text-sm border rounded p-2 ${currentUser && user.email === currentUser.email ? 'border-amspm-primary' : 'border-gray-300'}`}
                      autoFocus
                    />
                  ) : (
                    <span className="text-sm">{user.name || 'No name set'}</span>
                  )}
                </div>
                <div className="flex space-x-2 ml-2">
                  {isEditing(user.id) ? (
                    <>
                      <button
                        onClick={() => handleSaveName(user.id)}
                        className="text-green-600 hover:text-green-900 disabled:text-gray-400 disabled:cursor-not-allowed p-1"
                        disabled={!hasNameChanges(user.id)}
                        title="Save changes"
                      >
                        <FaSave size={16} />
                      </button>
                      <button
                        onClick={() => cancelEditing(user.id)}
                        className="text-gray-600 hover:text-gray-900 p-1"
                        title="Cancel editing"
                      >
                        <FaTimes size={16} />
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={() => startEditing(user.id)}
                      className="text-blue-600 hover:text-blue-900 p-1"
                      title="Edit user"
                    >
                      <FaEdit size={16} />
                    </button>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between mb-1">
                <p className="text-sm text-gray-500 font-medium">Role:</p>
                {isEditing(user.id) && !(currentUser && user.email === currentUser.email) ? (
                  <select
                    value={user.role}
                    onChange={(e) => handleUpdateRole(user.id, e.target.value, user.role)}
                    className="w-full text-sm border border-gray-300 rounded p-2 mt-1 bg-white text-amspm-text focus:border-amspm-primary focus:ring focus:ring-amspm-primary focus:ring-opacity-50"
                  >
                    <option value="administrator">Administrator</option>
                    <option value="verkoper">Verkoper</option>
                    <option value="monteur">Monteur</option>
                  </select>
                ) : (
                  <div className="flex items-center">
                    <span className={`px-2 py-1 text-xs rounded-full capitalize ${getRoleBadgeClass(user.role)}`}>
                      {user.role}
                    </span>
                    {currentUser && user.email === currentUser.email && (
                      <div className="ml-2 flex items-center text-gray-500" title="You cannot change your own role">
                        <FaLock size={12} />
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="border-t border-gray-200 pt-3 mt-3 space-y-2">
              {user.role !== "administrator" && (
                <button
                  onClick={() => handleManagePermissions(user.id)}
                  className="text-xs w-full flex items-center justify-center btn btn-secondary"
                  title="Manage user permissions"
                >
                  <FaUserCog className="mr-2" /> Manage Permissions
                </button>
              )}
              <button
                onClick={() => handleDelete(user.id)}
                className={`text-xs w-full flex items-center justify-center ${currentUser && user.email === currentUser.email ? 'btn btn-outline text-gray-500 cursor-not-allowed' : 'btn btn-danger'}`}
                title={currentUser && user.email === currentUser.email ? "You cannot delete your own account" : "Delete user"}
                disabled={currentUser && user.email === currentUser.email ? true : false}
              >
                <FaTrash className="mr-2" /> Delete User
              </button>
            </div>
          </div>
        ))}

        {users.length === 0 && (
          <div className="bg-white rounded-lg shadow p-4 text-center text-gray-500">
            No users found.
          </div>
        )}
      </div>

      <div className="mt-6">
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(totalItems / itemsPerPage)}
          onPageChange={handlePageChange}
          totalItems={totalItems}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={handleItemsPerPageChange}
        />
      </div>
    </div>
  );
};

export default Users;
