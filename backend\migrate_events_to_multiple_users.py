"""
Migration script to convert events from single user to multiple users.

This script:
1. Creates the event_users association table
2. Migrates existing user_id data to the new many-to-many relationship
3. Removes the user_id column from events table

Usage:
    python migrations/migrate_events_to_multiple_users.py
"""

import os
import sys
import logging

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("./logs/migration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def run_migration():
    """Run the migration to convert events to multiple users."""
    from app import create_app, db
    from sqlalchemy import text, inspect

    app_result = create_app()
    # Handle case where create_app returns a tuple (app, socketio)
    if isinstance(app_result, tuple):
        app = app_result[0]
    else:
        app = app_result

    with app.app_context():
        inspector = inspect(db.engine)

        try:
            # Step 1: Create event_users association table if it doesn't exist
            if not inspector.has_table('event_users'):
                logger.info("Creating event_users association table...")
                db.session.execute(text("""
                    CREATE TABLE event_users (
                        event_id INTEGER NOT NULL,
                        user_id INTEGER NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (event_id, user_id),
                        FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                    )
                """))
                db.session.commit()
                logger.info("Created event_users table")
            else:
                logger.info("event_users table already exists")

            # Step 2: Check if user_id column still exists in events table
            events_columns = [column['name'] for column in inspector.get_columns('events')]

            if 'user_id' in events_columns:
                logger.info("Migrating existing user_id data to event_users table...")

                # Migrate existing data
                db.session.execute(text("""
                    INSERT INTO event_users (event_id, user_id, created_at)
                    SELECT id, user_id, created_at
                    FROM events
                    WHERE user_id IS NOT NULL
                """))
                db.session.commit()
                logger.info("Migrated existing user assignments to event_users table")

                # Drop the user_id column
                logger.info("Dropping user_id column from events table...")
                db.session.execute(text("ALTER TABLE events DROP COLUMN user_id"))
                db.session.commit()
                logger.info("Dropped user_id column from events table")
            else:
                logger.info("user_id column has already been removed from events table")

            logger.info("Migration completed successfully!")

        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            db.session.rollback()
            raise

if __name__ == "__main__":
    run_migration()
