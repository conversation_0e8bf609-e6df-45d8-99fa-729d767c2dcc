#!/usr/bin/env python
"""
Script to generate a secure .env file for the Customer Management Application.
This script will prompt for necessary configuration values and create a .env file.
"""

import os
import secrets
import string
import json
from getpass import getpass

def generate_secret_key(length=32):
    """Generate a secure random secret key."""
    alphabet = string.ascii_letters + string.digits + string.punctuation
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def main():
    print("Customer Management Application - .env Generator")
    print("==============================================")
    print("This script will help you create a secure .env file for the application.")
    print("You'll need to provide some configuration values.")
    print()

    # Check if .env already exists
    if os.path.exists('.env'):
        overwrite = input(".env file already exists. Overwrite? (y/n): ").lower()
        if overwrite != 'y':
            print("Aborted.")
            return

    # Database configuration
    print("\n=== Database Configuration ===")
    db_host = input("Database host (default: localhost): ") or "localhost"
    db_port = input("Database port (default: 5432): ") or "5432"
    db_name = input("Database name (default: customer_management): ") or "customer_management"
    db_user = input("Database username: ")
    db_password = getpass("Database password: ")
    
    database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

    # Security
    print("\n=== Security Configuration ===")
    secret_key = generate_secret_key()
    print(f"Generated a secure SECRET_KEY")

    # Firebase configuration
    print("\n=== Firebase Configuration ===")
    print("Choose how to provide Firebase credentials:")
    print("1. Path to Firebase credentials file")
    print("2. Enter Firebase credentials JSON")
    
    firebase_choice = input("Enter your choice (1/2): ")
    
    firebase_credentials_path = ""
    firebase_credentials_json = ""
    
    if firebase_choice == "1":
        firebase_credentials_path = input("Path to Firebase credentials file: ")
    else:
        print("Enter Firebase credentials JSON (paste the entire JSON content):")
        print("Tip: You can paste multi-line content in most terminals.")
        firebase_credentials_json = input("> ")
        
        # Validate JSON
        try:
            json.loads(firebase_credentials_json)
        except json.JSONDecodeError:
            print("Error: Invalid JSON format. Please check your input.")
            return

    # Admin user
    print("\n=== Admin User Configuration ===")
    admin_email = input("Admin email address: ")

    # Logging
    print("\n=== Logging Configuration ===")
    log_file = input("Log file path (default: ./logs/app.log): ") or "./logs/app.log"

    # Cache configuration
    print("\n=== Cache Configuration ===")
    cache_type = input("Cache type (SimpleCache/RedisCache, default: SimpleCache): ") or "SimpleCache"
    
    redis_url = ""
    if cache_type.lower() == "rediscache":
        redis_host = input("Redis host (default: localhost): ") or "localhost"
        redis_port = input("Redis port (default: 6379): ") or "6379"
        redis_db = input("Redis database (default: 0): ") or "0"
        redis_url = f"redis://{redis_host}:{redis_port}/{redis_db}"
    else:
        redis_url = "redis://localhost:6379/0"
    
    cache_timeout = input("Cache timeout in seconds (default: 3600): ") or "3600"
    cache_key_prefix = input("Cache key prefix (default: customer_mgmt_): ") or "customer_mgmt_"

    # Write to .env file
    with open('.env', 'w') as f:
        f.write(f"# Database Configuration\n")
        f.write(f"DATABASE_URL={database_url}\n\n")
        
        f.write(f"# Security\n")
        f.write(f"SECRET_KEY={secret_key}\n\n")
        
        f.write(f"# Firebase Configuration\n")
        if firebase_credentials_path:
            f.write(f"FIREBASE_CREDENTIALS_PATH={firebase_credentials_path}\n")
        if firebase_credentials_json:
            f.write(f"FIREBASE_CREDENTIALS_JSON={firebase_credentials_json}\n")
        f.write(f"\n")
        
        f.write(f"# Admin User\n")
        f.write(f"ADMIN_EMAIL={admin_email}\n\n")
        
        f.write(f"# Logging\n")
        f.write(f"LOG_FILE={log_file}\n\n")
        
        f.write(f"# Cache Configuration\n")
        f.write(f"CACHE_TYPE={cache_type}\n")
        f.write(f"REDIS_URL={redis_url}\n")
        f.write(f"CACHE_TIMEOUT={cache_timeout}\n")
        f.write(f"CACHE_KEY_PREFIX={cache_key_prefix}\n")

    print("\n.env file has been created successfully!")
    print("Make sure to keep this file secure and never commit it to version control.")

if __name__ == "__main__":
    main()
