import React from 'react';
import AuditLogViewer from '../components/AuditLogViewer';
import { FaHistory } from 'react-icons/fa';

const AuditLogs: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-8 space-y-3 sm:space-y-0">
        <div className="flex items-center">
          <FaHistory className="text-amspm-primary mr-2 sm:mr-3 hidden sm:block" size={30} />
          <FaHistory className="text-amspm-primary mr-2 sm:hidden" size={24} />
          <h1 className="text-2xl sm:text-3xl font-bold text-amspm-text">Audit Logs</h1>
        </div>
      </div>
      
      <div className="mb-6">
        <p className="text-amspm-text mb-4">
          View a comprehensive history of all actions performed in the system. Audit logs help track changes and maintain accountability.
        </p>
        
        <div className="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mb-6">
          <p className="font-bold">Security Information</p>
          <p>All sensitive operations are logged with details about who performed the action, when it occurred, and what changes were made.</p>
        </div>
      </div>
      
      <AuditLogViewer />
    </div>
  );
};

export default AuditLogs;
