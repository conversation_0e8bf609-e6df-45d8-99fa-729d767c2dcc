import React, { useState } from 'react';
import { DocumentTemplate } from '../types/document_template';
import DocumentTemplateSelector from './DocumentTemplateSelector';
import TemplateFormEditor from './TemplateFormEditor';
import { createDocument } from '../services/documentService';
import LoadingSpinner from './LoadingSpinner';
import { FaArrowLeft, FaUpload, FaToggleOn, FaToggleOff } from 'react-icons/fa';

interface DocumentUploadWithTemplateProps {
  customerId: number;
  documentType: string;
  onSuccess: (documentId: number) => void;
  onCancel: () => void;
}

const DocumentUploadWithTemplate: React.FC<DocumentUploadWithTemplateProps> = ({
  customerId,
  documentType,
  onSuccess,
  onCancel
}) => {
  const [step, setStep] = useState<'select' | 'edit' | 'upload'>('select');
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [uploading, setUploading] = useState<boolean>(false);
  const [testMode, setTestMode] = useState<boolean>(false);
  const [expiryDate, setExpiryDate] = useState<string>('');
  const [expiryType, setExpiryType] = useState<"date" | "niet_van_toepassing">("date");

  const handleTemplateSelected = (template: DocumentTemplate) => {
    setSelectedTemplate(template);
    setStep('edit');
  };

  const handleSave = async (blob: Blob, fileName: string) => {
    try {
      setUploading(true);
      setError(null);

      // Create a File object from the Blob
      const file = new File([blob], fileName, { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });

      if (testMode) {
        // In test mode, just download the file
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();

        // Clean up
        setTimeout(() => {
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }, 0);

        // Show success message
        setError('Test mode: Document downloaded successfully. No upload was performed.');
      } else {
        // Upload the document
        const document = await createDocument(
          customerId,
          null, // No event ID
          file,
          documentType,
          expiryType, // Expiry type (date or niet_van_toepassing)
          expiryType === 'date' ? expiryDate : undefined, // Expiry date if applicable
          undefined, // No related document
          true, // This is an admin direct upload
          false // Not "not applicable"
        );

        onSuccess(document.id);
      }
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to upload document');
    } finally {
      setUploading(false);
    }
  };

  const handleBack = () => {
    if (step === 'edit') {
      setStep('select');
    } else {
      onCancel();
    }
  };

  return (
    <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-amspm-text">
          {step === 'select' ? 'Select Document Template' : 'Edit Document'}
        </h2>
        {step === 'edit' && (
          <button
            onClick={() => setStep('select')}
            className="btn btn-outline btn-sm flex items-center"
          >
            <FaArrowLeft className="mr-2" /> Back to Templates
          </button>
        )}
      </div>

      {error && (
        <p className={`mb-4 ${error.startsWith('Test mode:') ? 'text-green-600 dark:text-green-400' : 'text-red-500'}`}>
          {error}
        </p>
      )}

      {uploading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-dark-card p-6 rounded-lg shadow-lg max-w-md w-full">
            <LoadingSpinner />
            <p className="text-center mt-4 text-amspm-text dark:text-dark-text">Uploading document...</p>
          </div>
        </div>
      )}

      {step === 'select' && (
        <DocumentTemplateSelector
          documentType={documentType}
          onTemplateSelected={handleTemplateSelected}
        />
      )}

      {step === 'edit' && selectedTemplate && (
        <div>
          <div className="mb-4 flex justify-between items-center">
            <div className="flex items-center space-x-4">
              {/* Expiry Type Selection */}
              <div className="form-group">
                <label className="block text-amspm-text font-medium mb-1 text-sm">Expiry Type</label>
                <select
                  value={expiryType}
                  onChange={(e) => {
                    setExpiryType(e.target.value as "date" | "niet_van_toepassing");
                    if (e.target.value === "niet_van_toepassing") setExpiryDate("");
                  }}
                  className="input input-sm"
                  disabled={uploading}
                >
                  <option value="date">Date</option>
                  <option value="niet_van_toepassing">Niet van toepassing</option>
                </select>
              </div>

              {/* Expiry Date Field */}
              {expiryType === "date" && (
                <div className="form-group">
                  <label className="block text-amspm-text font-medium mb-1 text-sm">Expiry Date</label>
                  <input
                    type="datetime-local"
                    value={expiryDate}
                    onChange={(e) => setExpiryDate(e.target.value)}
                    className="input input-sm"
                    disabled={uploading}
                  />
                </div>
              )}
            </div>

            {/* Test Mode Toggle */}
            <div
              className="flex items-center cursor-pointer bg-gray-100 dark:bg-dark-secondary rounded-md px-3 py-1"
              onClick={() => setTestMode(!testMode)}
            >
              {testMode ? (
                <>
                  <FaToggleOn className="text-green-500 mr-2" size={20} />
                  <span className="text-sm font-medium">Test Mode: ON</span>
                </>
              ) : (
                <>
                  <FaToggleOff className="text-gray-500 mr-2" size={20} />
                  <span className="text-sm font-medium">Test Mode: OFF</span>
                </>
              )}
            </div>
          </div>

          {testMode && (
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
              <p className="text-blue-700 dark:text-blue-300 text-sm">
                <strong>Test Mode Enabled:</strong> When you click Save, the document will be downloaded instead of uploaded to the customer. This allows you to verify the document format before uploading.
              </p>
              <p className="text-blue-700 dark:text-blue-300 text-sm mt-2">
                <strong>Note:</strong> When you upload the document, it will be saved with the selected expiry settings and will behave like any other document, including:
              </p>
              <ul className="list-disc list-inside text-blue-700 dark:text-blue-300 text-sm ml-4 mt-1">
                <li>Active status tracking (new uploads will mark previous documents as inactive)</li>
                <li>Expiry date monitoring (documents will appear in upcoming expirations)</li>
                <li>Document history preservation (previous versions remain accessible)</li>
              </ul>
            </div>
          )}

          <TemplateFormEditor
            template={selectedTemplate}
            onSave={handleSave}
            onCancel={() => setStep('select')}
            testMode={testMode}
          />
        </div>
      )}
    </div>
  );
};

export default DocumentUploadWithTemplate;
