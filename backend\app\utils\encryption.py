"""
Encryption utilities for field-level encryption of sensitive data.

This module provides functions for encrypting and decrypting sensitive data
using AES-256-GCM symmetric encryption.
"""

import os
import base64
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from flask import current_app
import json
import logging

logger = logging.getLogger(__name__)

# Constants
NONCE_SIZE = 12  # GCM recommended nonce size
KDF_ITERATIONS = 100000  # Number of iterations for key derivation
SALT_SIZE = 16  # Size of salt for key derivation
KEY_SIZE = 32  # AES-256 key size (32 bytes)

class EncryptionError(Exception):
    """Exception raised for encryption-related errors."""
    pass

def get_encryption_key():
    """
    Get the encryption key from the application configuration.

    The key is derived from the master key using PBKDF2.

    Returns:
        bytes: The derived encryption key
    """
    try:
        # Check if we're in an application context
        try:
            from flask import current_app
            # Get master key from environment via app config
            master_key = current_app.config.get('ENCRYPTION_KEY')

            # Use a fixed salt for key derivation (stored with app config)
            salt = current_app.config.get('ENCRYPTION_SALT')
            if not salt:
                # Generate a salt if not available
                salt = os.urandom(SALT_SIZE)
                current_app.config['ENCRYPTION_SALT'] = salt
            elif isinstance(salt, str):
                # Convert from base64 if stored as string
                salt = base64.b64decode(salt)
        except RuntimeError:
            # Not in app context, try to get from environment directly
            import os
            master_key = os.getenv('ENCRYPTION_KEY')
            salt_str = os.getenv('ENCRYPTION_SALT')
            if salt_str:
                salt = base64.b64decode(salt_str)
            else:
                # Use a default salt (this is just for model definition, not actual encryption)
                salt = b'default_salt_for_model_definition'

        if not master_key:
            raise EncryptionError("Encryption key not configured")

        # Convert string key to bytes
        if isinstance(master_key, str):
            master_key = master_key.encode('utf-8')

        # Derive the key using PBKDF2
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=KEY_SIZE,
            salt=salt,
            iterations=KDF_ITERATIONS,
        )
        derived_key = kdf.derive(master_key)

        return derived_key
    except Exception as e:
        logger.error(f"Error getting encryption key: {str(e)}")
        raise EncryptionError(f"Error getting encryption key: {str(e)}")

def encrypt(data):
    """
    Encrypt data using AES-256-GCM.

    Args:
        data: The data to encrypt (can be any JSON-serializable type)

    Returns:
        str: Base64-encoded encrypted data with nonce

    Raises:
        EncryptionError: If encryption fails
    """
    if data is None:
        return None

    try:
        # Serialize data to JSON if it's not a string
        if not isinstance(data, str):
            data = json.dumps(data)

        # Convert to bytes
        plaintext = data.encode('utf-8')

        # Get the encryption key
        key = get_encryption_key()

        # Generate a random nonce
        nonce = os.urandom(NONCE_SIZE)

        # Create an AESGCM instance
        aesgcm = AESGCM(key)

        # Encrypt the data
        ciphertext = aesgcm.encrypt(nonce, plaintext, None)

        # Combine nonce and ciphertext and encode as base64
        encrypted_data = base64.b64encode(nonce + ciphertext).decode('utf-8')

        return encrypted_data
    except Exception as e:
        logger.error(f"Encryption error: {str(e)}")
        raise EncryptionError(f"Encryption error: {str(e)}")

def decrypt(encrypted_data):
    """
    Decrypt data using AES-256-GCM.

    Args:
        encrypted_data (str): Base64-encoded encrypted data with nonce

    Returns:
        The decrypted data (string or deserialized JSON)

    Raises:
        EncryptionError: If decryption fails
    """
    if encrypted_data is None:
        return None

    # Add debug logging to help diagnose issues
    logger.debug(f"Attempting to decrypt data: {encrypted_data[:20]}...")

    try:
        # Check if the data is already decrypted (not base64 encoded)
        # This is a simple heuristic - encrypted data is base64 and longer
        if not (isinstance(encrypted_data, str) and len(encrypted_data) > 20 and '=' in encrypted_data):
            # Data appears to be already decrypted or not in the expected format
            logger.debug("Data appears to be already decrypted or not in the expected format")
            return encrypted_data

        # Decode from base64
        try:
            data = base64.b64decode(encrypted_data)
            logger.debug(f"Successfully decoded base64 data, length: {len(data)}")
        except Exception as e:
            # If we can't decode as base64, return the original data
            logger.warning(f"Could not decode as base64, returning original data: {str(e)}")
            return encrypted_data

        # Check if the data is long enough to contain a nonce
        if len(data) <= NONCE_SIZE:
            logger.warning(f"Data too short to contain nonce, returning original data")
            return encrypted_data

        # Extract nonce and ciphertext
        nonce = data[:NONCE_SIZE]
        ciphertext = data[NONCE_SIZE:]
        logger.debug(f"Extracted nonce (length: {len(nonce)}) and ciphertext (length: {len(ciphertext)})")

        # Get the encryption key
        key = get_encryption_key()

        # Create an AESGCM instance
        aesgcm = AESGCM(key)

        # Decrypt the data
        try:
            plaintext = aesgcm.decrypt(nonce, ciphertext, None)
            logger.debug("Successfully decrypted data")
        except Exception as e:
            logger.error(f"AESGCM decryption failed: {str(e)}")
            return encrypted_data

        # Convert from bytes to string
        try:
            decrypted_data = plaintext.decode('utf-8')
            logger.debug(f"Decoded plaintext to string: {decrypted_data[:20]}...")
        except UnicodeDecodeError as e:
            logger.error(f"Failed to decode plaintext as UTF-8: {str(e)}")
            return encrypted_data

        # Try to deserialize from JSON
        try:
            result = json.loads(decrypted_data)
            logger.debug("Successfully parsed JSON data")
            return result
        except json.JSONDecodeError:
            # Return as string if not valid JSON
            logger.debug("Not valid JSON, returning as string")
            return decrypted_data
    except Exception as e:
        logger.error(f"Decryption error: {str(e)}")
        # Return the original data instead of raising an exception
        logger.warning(f"Returning original data due to decryption error")
        return encrypted_data

# SQLAlchemy type for encrypted fields
from sqlalchemy.types import TypeDecorator, String

class EncryptedType(TypeDecorator):
    """
    SQLAlchemy type for encrypted fields.

    This type transparently encrypts data when writing to the database
    and decrypts it when reading from the database.
    """
    impl = String
    cache_ok = False

    def __init__(self, length=None, **kwargs):
        # Encrypted data can be significantly longer than the original
        if length:
            # Add extra space for encryption overhead
            length = max(length * 2, 255)
        super(EncryptedType, self).__init__(length, **kwargs)

    def process_bind_param(self, value, dialect):
        """Encrypt data before storing in the database."""
        if value is not None:
            # Check if the value is already encrypted
            if isinstance(value, str) and len(value) > 20 and '=' in value:
                try:
                    # Try to decode as base64 to verify it's encrypted
                    base64.b64decode(value)
                    # If we get here, it's likely already encrypted
                    logger.debug(f"Value appears to be already encrypted, skipping encryption")
                    return value
                except Exception:
                    # Not valid base64, proceed with encryption
                    pass

            try:
                encrypted = encrypt(value)
                logger.debug(f"Successfully encrypted value")
                return encrypted
            except Exception as e:
                logger.error(f"Error encrypting value: {str(e)}")
                # Return the original value if encryption fails
                return value
        return None

    def process_result_value(self, value, dialect):
        """Decrypt data when retrieving from the database."""
        if value is not None:
            try:
                decrypted = decrypt(value)
                logger.debug(f"Successfully decrypted value")
                return decrypted
            except Exception as e:
                logger.error(f"Error decrypting value: {str(e)}")
                # Return the original value if decryption fails
                return value
        return None
