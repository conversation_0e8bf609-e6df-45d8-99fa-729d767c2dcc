"""
Migration script to decrypt customer data and update the model to stop using encryption.

This script:
1. Retrieves all customers from the database
2. Decrypts all encrypted fields
3. Updates the database with the decrypted data

Usage:
    python migrations/decrypt_customer_data.py
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("./logs/migration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def decrypt_data():
    """
    Decrypt customer data in the database.

    This function:
    1. Retrieves all customers from the database
    2. For each record, decrypts sensitive fields
    3. Updates the database with the decrypted data

    Returns:
        dict: Statistics about the decryption process
    """
    # Import here to avoid circular imports
    import sys
    sys.path.append('..')  # Add parent directory to path

    # First, create the Flask application
    from app import create_app, db
    app, _ = create_app()  # Get the app and ignore the socketio return value

    # Push an application context
    with app.app_context():
        # Now import models that need app context
        from app.models.customer import Customer
        from app.utils.encryption import decrypt

        stats = {
            'total_customers': 0,
            'decrypted_fields': 0,
            'errors': []
        }

        try:
            # Get all customers
            customers = Customer.query.all()
            stats['total_customers'] = len(customers)
            logger.info(f"Found {len(customers)} customers to decrypt")

            # List of fields that should be decrypted
            encrypted_fields = [
                'address', 'postal_code', 'city',
                'address2', 'postal_code2', 'city2',
                'phone', 'mobile', 'fax', 'email',
                'invoice_email', 'reminder_email',
                'bank_account', 'giro_account', 'vat_number',
                'iban', 'bic'
            ]

            # Process each customer
            for customer in customers:
                try:
                    # Decrypt each field
                    for field in encrypted_fields:
                        value = getattr(customer, field)
                        if value and isinstance(value, str) and len(value) > 20 and '=' in value:
                            # This looks like it might be encrypted, try to decrypt it
                            decrypted_value = decrypt(value)
                            setattr(customer, field, decrypted_value)
                            stats['decrypted_fields'] += 1

                    # Update the customer in the database
                    db.session.add(customer)

                except Exception as e:
                    error_msg = f"Error decrypting customer {customer.id}: {str(e)}"
                    logger.error(error_msg)
                    stats['errors'].append(error_msg)

            # Commit all changes
            db.session.commit()
            logger.info(f"Successfully decrypted {stats['decrypted_fields']} fields across {stats['total_customers']} customers")

        except Exception as e:
            error_msg = f"Error during decryption: {str(e)}"
            logger.error(error_msg)
            stats['errors'].append(error_msg)
            db.session.rollback()

        return stats

def update_customer_model():
    """
    Update the Customer model to stop using encryption.

    This function modifies the customer.py file to replace EncryptedType with regular String types.
    """
    try:
        # Path to the customer model file
        model_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                                 'app', 'models', 'customer.py')

        # Read the current file content
        with open(model_path, 'r') as file:
            content = file.read()

        # Replace the get_field_type function and its usage
        updated_content = content.replace(
            '# Move get_field_type outside the class to avoid evaluation during class definition\ndef get_field_type(field_type, length):\n    """Helper to determine if a field should be encrypted"""\n    # Use EncryptedType by default - will be properly initialized when app context is available\n    return EncryptedType(length)',
            '# Function removed - no longer using encryption\ndef get_field_type(field_type, length):\n    """Helper function kept for backward compatibility"""\n    return field_type(length)'
        )

        # Write the updated content back to the file
        with open(model_path, 'w') as file:
            file.write(updated_content)

        logger.info(f"Successfully updated Customer model to stop using encryption")
        return True
    except Exception as e:
        logger.error(f"Error updating Customer model: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting customer data decryption migration")

    # First decrypt all data
    stats = decrypt_data()

    # Print summary of decryption
    logger.info(f"Decryption completed:")
    logger.info(f"  Total customers: {stats.get('total_customers', 0)}")
    logger.info(f"  Decrypted fields: {stats.get('decrypted_fields', 0)}")

    if stats.get('errors'):
        logger.info(f"  Errors: {len(stats['errors'])}")
        for i, error in enumerate(stats['errors'][:10]):
            logger.info(f"    {i+1}. {error}")
        if len(stats['errors']) > 10:
            logger.info(f"    ... and {len(stats['errors']) - 10} more errors")

    # Then update the model
    if update_customer_model():
        logger.info("Customer model updated successfully to stop using encryption")
    else:
        logger.error("Failed to update Customer model")

    logger.info("Migration completed")
