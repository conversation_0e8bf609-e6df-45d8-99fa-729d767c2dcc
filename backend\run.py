from app import create_app
import ssl
import os

app, _ = create_app()  # socketio removed

if __name__ == "__main__":
    ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
    ssl_context.load_cert_chain(
        certfile=os.path.abspath("./certs/cert.pem"),
        keyfile=os.path.abspath("./certs/key.pem")
    )
    # Use app.run - WebSocket support removed
    # Get debug mode from environment, default to False for security
    debug_mode = os.getenv("FLASK_DEBUG", "False").lower() in ("true", "1", "t")

    # In production, bind to localhost instead of all interfaces (0.0.0.0)
    host = "localhost" if not debug_mode else "0.0.0.0"

    app.run(host=host, port=5000, ssl_context=ssl_context, debug=debug_mode, threaded=True)