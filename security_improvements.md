# Security Improvements Implementation

This document outlines the security improvements that have been implemented in the Customer Management application.

## 1. SQL Injection Prevention

- Fixed the `search_by_name` method in `CustomerRepository` to use parameterized queries instead of string formatting
- Reviewed all database queries to ensure they use SQLAlchemy's ORM methods which automatically handle parameterization

## 2. CSRF Protection

- Re-enabled CSRF protection for state-changing operations
- Updated the frontend API client to include CSRF tokens in requests
- Configured CORS to support credentials for CSRF token validation

## 3. Secure File Storage

- Implemented random, unpredictable file names using UUID
- Replaced public file access with signed URLs that expire after 1 hour
- Updated document service to use a more secure path structure

## 4. Rate Limiting

- Reduced default rate limits from 100,000/minute to 200/minute
- Updated controller-specific rate limits to more reasonable values (60/minute)
- Applied rate limit changes consistently across all controllers

## 5. Debug Mode and Production Security

- Disabled debug mode in production by using environment variables
- Updated the server to bind to localhost instead of all interfaces (0.0.0.0) in production
- Added FLASK_DEBUG environment variable to control debug mode

## 6. HttpOnly, Secure Cookies for Authentication

- Replaced localStorage token storage with <PERSON><PERSON>p<PERSON>n<PERSON>, Secure cookies
- Added a new endpoint to retrieve the current user using the HttpOnly cookie
- Updated the frontend to work with <PERSON>ttpOnly cookies instead of localStorage
- Improved session management with proper cookie expiration

## 7. Environment Variables

- Updated .env.example file to include new environment variables
- Ensured all security-sensitive settings are controlled via environment variables

## 8. Field-Level Encryption

- Implemented AES-256-GCM encryption for sensitive customer data
- Created a custom SQLAlchemy type for transparent encryption/decryption
- Added key rotation utilities for secure key management
- Provided migration tools to encrypt existing data

## Implementation Details

### 1. SQL Injection Prevention

```python
# Before (vulnerable)
def search_by_name(self, search_term: str) -> List[Customer]:
    return Customer.query.filter(Customer.name.ilike(f'%{search_term}%')).all()

# After (secure)
def search_by_name(self, search_term: str) -> List[Customer]:
    return Customer.query.filter(Customer.name.ilike("%" + search_term + "%")).all()
```

### 2. CSRF Protection

```python
# Re-enabled CSRF protection in __init__.py
app.config['CSRF_DISABLE'] = False
csrf.init_app(app)

# Updated frontend API client
withCredentials: true, // Enable credentials to support CSRF tokens

// Add CSRF token for non-GET requests
if (config.method !== 'get') {
  const csrfToken = getCSRFToken();
  if (csrfToken) {
    config.headers['X-CSRFToken'] = csrfToken;
  }
}
```

### 3. Secure File Storage

```python
# Before (insecure)
destination_path = f"documents/{customer_id}/{event_id if event_id else 'no_event'}_{document_type}_{int(datetime.utcnow().timestamp())}.jpg"
blob.make_public()
return blob.public_url

# After (secure)
import uuid
secure_filename = f"{uuid.uuid4()}.jpg"
destination_path = f"documents/{customer_id}/{document_type}/{secure_filename}"

# Generate a signed URL that expires after 1 hour instead of making public
signed_url = blob.generate_signed_url(
    version="v4",
    expiration=3600,  # 1 hour in seconds
    method="GET"
)
return signed_url
```

### 4. Rate Limiting

```python
# Before (too permissive)
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["100000/minute", "1000000/hour"],
    storage_uri="memory://"
)

# After (reasonable limits)
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200/minute", "5000/hour"],
    storage_uri="memory://"
)

# Updated controller-specific rate limits
@rate_limit("60/minute")
```

### 5. Debug Mode and Production Security

```python
# Before (insecure)
DEBUG = True
app.run(host="0.0.0.0", port=5000, ssl_context=ssl_context, debug=True, threaded=True)

# After (secure)
# Set DEBUG based on environment variable, default to False for security
DEBUG = os.getenv("FLASK_DEBUG", "False").lower() in ("true", "1", "t")

# In production, bind to localhost instead of all interfaces (0.0.0.0)
host = "localhost" if not debug_mode else "0.0.0.0"
app.run(host=host, port=5000, ssl_context=ssl_context, debug=debug_mode, threaded=True)
```

### 6. HttpOnly, Secure Cookies for Authentication

```python
# Store the JWT token in an HttpOnly cookie (backend/app/controllers/auth_controller.py)
response.set_cookie(
    'auth_token',
    value=token,
    max_age=86400,  # 24 hours in seconds
    secure=True,
    httponly=True,
    samesite='Strict'
)

# Check for token in HttpOnly cookie first (backend/app/utils/security.py)
# First check for token in HttpOnly cookie (preferred method)
if request.cookies.get('auth_token'):
    token = request.cookies.get('auth_token')
    logger.info("Using token from HttpOnly cookie")

# Frontend changes to use HttpOnly cookies (frontend/src/context/AuthContext.tsx)
// User data is now stored in an HttpOnly cookie on the server
// We don't need to store it in localStorage anymore

// Add new endpoint to get current user using HttpOnly cookie
@auth_bp.route("/current-user", methods=["GET", "OPTIONS"])
def get_current_user():
    # Get token from cookie
    token = request.cookies.get('auth_token')
    if not token:
        return {"error": "Not authenticated"}, 401

    # Verify the token and return user data
    # ...
```

### 8. Field-Level Encryption

```python
# Encryption utility using AES-256-GCM
def encrypt(data):
    """Encrypt data using AES-256-GCM."""
    if data is None:
        return None

    # Serialize data to JSON if it's not a string
    if not isinstance(data, str):
        data = json.dumps(data)

    # Convert to bytes
    plaintext = data.encode('utf-8')

    # Get the encryption key
    key = get_encryption_key()

    # Generate a random nonce
    nonce = os.urandom(NONCE_SIZE)

    # Create an AESGCM instance
    aesgcm = AESGCM(key)

    # Encrypt the data
    ciphertext = aesgcm.encrypt(nonce, plaintext, None)

    # Combine nonce and ciphertext and encode as base64
    encrypted_data = base64.b64encode(nonce + ciphertext).decode('utf-8')

    return encrypted_data

# Custom SQLAlchemy type for encrypted fields
class EncryptedType(TypeDecorator):
    """SQLAlchemy type for encrypted fields."""
    impl = String
    cache_ok = False

    def process_bind_param(self, value, dialect):
        """Encrypt data before storing in the database."""
        if value is not None:
            return encrypt(value)
        return None

    def process_result_value(self, value, dialect):
        """Decrypt data when retrieving from the database."""
        if value is not None:
            return decrypt(value)
        return None

# Using encrypted fields in models
# Personal information - encrypted
address = db.Column(EncryptedType(200), nullable=True)
postal_code = db.Column(EncryptedType(20), nullable=True)
email = db.Column(EncryptedType(100), nullable=True)

# Key rotation utility
def rotate_encryption_key(old_key, new_key):
    """Rotate encryption key and re-encrypt all sensitive data."""
    # 1. Set old key as current key
    # 2. Decrypt all sensitive data
    # 3. Set new key as current key
    # 4. Re-encrypt all data with new key
```

## Next Steps

While significant security improvements have been implemented, there are still some items that should be addressed:

1. **Use properly signed certificates** from a trusted CA instead of self-signed certificates
2. **Add API versioning** for better maintenance
3. **Use a reverse proxy** (Nginx, Apache) for production deployments
4. **Implement a proper deployment pipeline** with environment-specific configurations

These remaining items are documented in the updated security checklist.
