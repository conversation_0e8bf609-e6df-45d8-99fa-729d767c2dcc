import React, { useState } from 'react';
import { FaBoxOpen, FaFileExcel } from 'react-icons/fa';
import Breadcrumbs from '../components/Breadcrumbs';
import ProductList from '../components/products/ProductList';
import ProductImport from '../components/products/ProductImport';
import { useAuth } from '../context/AuthContext';

const ProductsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'list' | 'import'>('list');
  const { user } = useAuth();
  const isAdmin = user?.role === 'administrator';

  return (
    <div className="container mx-auto px-4 py-8">
      <Breadcrumbs
        items={[
          { label: 'Dashboard', path: '/dashboard' },
          { label: 'Producten', path: '/products' },
        ]}
      />
      
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-amspm-text dark:text-dark-text flex items-center">
          <FaBoxOpen className="mr-2" /> Producten
        </h1>
        <p className="text-gray-600 dark:text-dark-text-light mt-2">
          Beheer producten voor offertes en prijslijsten.
        </p>
      </div>
      
      <div className="mb-6">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('list')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'list'
                  ? 'border-amspm-primary text-amspm-primary dark:border-dark-accent dark:text-dark-accent'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <FaBoxOpen className="inline-block mr-2" /> Productenlijst
            </button>
            
            {isAdmin && (
              <button
                onClick={() => setActiveTab('import')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'import'
                    ? 'border-amspm-primary text-amspm-primary dark:border-dark-accent dark:text-dark-accent'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <FaFileExcel className="inline-block mr-2" /> OSEC Prijslijst Importeren
              </button>
            )}
          </nav>
        </div>
      </div>
      
      {activeTab === 'list' ? (
        <ProductList />
      ) : (
        <ProductImport onImportSuccess={() => setActiveTab('list')} />
      )}
    </div>
  );
};

export default ProductsPage;
