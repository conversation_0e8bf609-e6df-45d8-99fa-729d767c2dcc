"""
Utility functions for key rotation and re-encryption of sensitive data.

This module provides functions to help with rotating encryption keys
and re-encrypting data with new keys.
"""

import os
import base64
import logging
from flask import current_app
from app import db
from app.models.customer import Customer
from app.utils.encryption import encrypt, decrypt, EncryptionError

logger = logging.getLogger(__name__)

def generate_new_encryption_key():
    """
    Generate a new encryption key.

    Returns:
        str: Base64-encoded encryption key
    """
    # Generate a random 32-byte key (256 bits)
    key = os.urandom(32)
    # Encode as base64 for storage
    return base64.b64encode(key).decode('utf-8')

def generate_new_salt():
    """
    Generate a new salt for key derivation.

    Returns:
        str: Base64-encoded salt
    """
    # Generate a random 16-byte salt
    salt = os.urandom(16)
    # Encode as base64 for storage
    return base64.b64encode(salt).decode('utf-8')

def rotate_encryption_key(old_key, new_key, old_salt=None, new_salt=None):
    """
    Rotate the encryption key and re-encrypt all sensitive data.

    This function should be run as a maintenance task when rotating encryption keys.
    It will:
    1. Temporarily set the old key as the current key
    2. Decrypt all sensitive data
    3. Set the new key as the current key
    4. Re-encrypt all sensitive data with the new key

    Args:
        old_key (str): The old encryption key
        new_key (str): The new encryption key
        old_salt (str, optional): The old salt for key derivation
        new_salt (str, optional): The new salt for key derivation

    Returns:
        dict: Statistics about the re-encryption process
    """
    from app.models.document import Document

    stats = {
        'total_customers': 0,
        'successful_customers': 0,
        'failed_customers': 0,
        'total_documents': 0,
        'successful_documents': 0,
        'failed_documents': 0,
        'errors': []
    }

    # Store original key and salt
    original_key = current_app.config.get('ENCRYPTION_KEY')
    original_salt = current_app.config.get('ENCRYPTION_SALT')

    try:
        # PART 1: Rotate Customer encryption keys
        # Get all customers
        customers = Customer.query.all()
        stats['total_customers'] = len(customers)
        logger.info(f"Found {stats['total_customers']} customers to re-encrypt")

        # Process each customer
        for customer in customers:
            try:
                # Step 1: Set old key and salt to decrypt data
                current_app.config['ENCRYPTION_KEY'] = old_key
                if old_salt:
                    current_app.config['ENCRYPTION_SALT'] = old_salt

                # Step 2: Decrypt sensitive fields
                # Create a dictionary to store decrypted values
                decrypted_data = {}

                # List of encrypted fields to process
                customer_encrypted_fields = [
                    'address', 'postal_code', 'city',
                    'address2', 'postal_code2', 'city2',
                    'phone', 'mobile', 'fax', 'email',
                    'invoice_email', 'reminder_email',
                    'bank_account', 'giro_account', 'vat_number',
                    'iban', 'bic'
                ]

                # Decrypt all fields
                for field in customer_encrypted_fields:
                    encrypted_value = getattr(customer, field)
                    if encrypted_value:
                        try:
                            # Direct decryption bypassing the model's automatic decryption
                            decrypted_value = decrypt(encrypted_value)
                            decrypted_data[field] = decrypted_value
                        except EncryptionError as e:
                            logger.warning(f"Could not decrypt {field} for customer {customer.id}: {str(e)}")
                            # Keep the original value if decryption fails
                            decrypted_data[field] = encrypted_value

                # Step 3: Set new key and salt for encryption
                current_app.config['ENCRYPTION_KEY'] = new_key
                if new_salt:
                    current_app.config['ENCRYPTION_SALT'] = new_salt

                # Step 4: Re-encrypt all fields with the new key
                for field, value in decrypted_data.items():
                    if value:
                        try:
                            # Direct encryption bypassing the model's automatic encryption
                            encrypted_value = encrypt(value)
                            setattr(customer, field, encrypted_value)
                        except EncryptionError as e:
                            logger.error(f"Could not re-encrypt {field} for customer {customer.id}: {str(e)}")
                            # Keep track of errors
                            stats['errors'].append(f"Customer {customer.id}, field {field}: {str(e)}")

                # Save the customer with re-encrypted data
                db.session.add(customer)
                stats['successful_customers'] += 1

                # Commit periodically to avoid large transactions
                if stats['successful_customers'] % 100 == 0:
                    logger.info(f"Processed {stats['successful_customers']} customers")
                    db.session.commit()

            except Exception as e:
                logger.error(f"Error processing customer {customer.id}: {str(e)}")
                stats['failed_customers'] += 1
                stats['errors'].append(f"Customer {customer.id}: {str(e)}")

        # Commit customer changes
        db.session.commit()
        logger.info(f"Customer key rotation completed: {stats['successful_customers']} successful, {stats['failed_customers']} failed")

        # PART 2: Rotate Document encryption keys
        # Get all documents
        documents = Document.query.all()
        stats['total_documents'] = len(documents)
        logger.info(f"Found {stats['total_documents']} documents to re-encrypt")

        # Process each document
        for document in documents:
            try:
                # Step 1: Set old key and salt to decrypt data
                current_app.config['ENCRYPTION_KEY'] = old_key
                if old_salt:
                    current_app.config['ENCRYPTION_SALT'] = old_salt

                # Step 2: Decrypt sensitive fields
                # Create a dictionary to store decrypted values
                decrypted_data = {}

                # List of encrypted fields to process
                document_encrypted_fields = [
                    'file_url', 'file_path'
                ]

                # Decrypt all fields
                for field in document_encrypted_fields:
                    encrypted_value = getattr(document, field)
                    if encrypted_value:
                        try:
                            # Direct decryption bypassing the model's automatic decryption
                            decrypted_value = decrypt(encrypted_value)
                            decrypted_data[field] = decrypted_value
                        except EncryptionError as e:
                            logger.warning(f"Could not decrypt {field} for document {document.id}: {str(e)}")
                            # Keep the original value if decryption fails
                            decrypted_data[field] = encrypted_value

                # Step 3: Set new key and salt for encryption
                current_app.config['ENCRYPTION_KEY'] = new_key
                if new_salt:
                    current_app.config['ENCRYPTION_SALT'] = new_salt

                # Step 4: Re-encrypt all fields with the new key
                for field, value in decrypted_data.items():
                    if value:
                        try:
                            # Direct encryption bypassing the model's automatic encryption
                            encrypted_value = encrypt(value)
                            setattr(document, field, encrypted_value)
                        except EncryptionError as e:
                            logger.error(f"Could not re-encrypt {field} for document {document.id}: {str(e)}")
                            # Keep track of errors
                            stats['errors'].append(f"Document {document.id}, field {field}: {str(e)}")

                # Save the document with re-encrypted data
                db.session.add(document)
                stats['successful_documents'] += 1

                # Commit periodically to avoid large transactions
                if stats['successful_documents'] % 100 == 0:
                    logger.info(f"Processed {stats['successful_documents']} documents")
                    db.session.commit()

            except Exception as e:
                logger.error(f"Error processing document {document.id}: {str(e)}")
                stats['failed_documents'] += 1
                stats['errors'].append(f"Document {document.id}: {str(e)}")

        # Commit document changes
        db.session.commit()
        logger.info(f"Document key rotation completed: {stats['successful_documents']} successful, {stats['failed_documents']} failed")

    except Exception as e:
        logger.error(f"Error during key rotation: {str(e)}")
        db.session.rollback()
        stats['errors'].append(f"Global error: {str(e)}")
    finally:
        # Restore original key and salt
        current_app.config['ENCRYPTION_KEY'] = original_key
        current_app.config['ENCRYPTION_SALT'] = original_salt

    return stats

def create_key_rotation_command():
    """
    Create a Flask CLI command for key rotation.

    This function creates a command that can be registered with Flask's CLI
    to perform key rotation from the command line.

    Example usage:
        flask rotate-encryption-key --old-key OLD_KEY --new-key NEW_KEY
    """
    import click
    from flask.cli import with_appcontext

    @click.command('rotate-encryption-key')
    @click.option('--old-key', required=True, help='The old encryption key')
    @click.option('--new-key', required=True, help='The new encryption key')
    @click.option('--old-salt', help='The old salt for key derivation')
    @click.option('--new-salt', help='The new salt for key derivation')
    @with_appcontext
    def rotate_key_command(old_key, new_key, old_salt, new_salt):
        """Rotate encryption key and re-encrypt all sensitive data."""
        click.echo('Starting encryption key rotation...')
        stats = rotate_encryption_key(old_key, new_key, old_salt, new_salt)

        # Print summary
        click.echo(f"Key rotation completed:")
        click.echo(f"  Total customers: {stats.get('total_customers', 0)}")
        click.echo(f"  Successfully re-encrypted customers: {stats.get('successful_customers', 0)}")
        click.echo(f"  Failed customers: {stats.get('failed_customers', 0)}")
        click.echo(f"  Total documents: {stats.get('total_documents', 0)}")
        click.echo(f"  Successfully re-encrypted documents: {stats.get('successful_documents', 0)}")
        click.echo(f"  Failed documents: {stats.get('failed_documents', 0)}")

        if stats['errors']:
            click.echo(f"Errors encountered: {len(stats['errors'])}")
            for error in stats['errors'][:10]:  # Show first 10 errors
                click.echo(f"  - {error}")
            if len(stats['errors']) > 10:
                click.echo(f"  ... and {len(stats['errors']) - 10} more errors")

    return rotate_key_command
