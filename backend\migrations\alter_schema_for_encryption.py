"""
Migration script to alter database schema for encryption.

This script should be run before encrypting existing customer data
to ensure that the database columns are large enough to store encrypted data.

Usage:
    python migrations/alter_schema_for_encryption.py
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("./logs/migration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def alter_schema():
    """
    Alter database schema to accommodate encrypted data.
    
    This function:
    1. Increases column sizes for fields that will be encrypted
    
    Returns:
        dict: Statistics about the schema alteration
    """
    # Import here to avoid circular imports
    import sys
    sys.path.append('..')  # Add parent directory to path

    # First, create the Flask application
    from app import create_app, db
    app, _ = create_app()  # Get the app and ignore the socketio return value

    # Push an application context
    with app.app_context():
        from sqlalchemy import text
        
        stats = {
            'altered_columns': 0,
            'errors': []
        }
        
        try:
            # Define the columns to alter and their new sizes
            columns_to_alter = [
                # Customer table
                ('customers', 'postal_code', 255),
                ('customers', 'bic', 255),
                ('customers', 'address', 500),
                ('customers', 'postal_code2', 255),
                ('customers', 'city', 255),
                ('customers', 'city2', 255),
                ('customers', 'phone', 255),
                ('customers', 'mobile', 255),
                ('customers', 'fax', 255),
                ('customers', 'email', 255),
                ('customers', 'invoice_email', 255),
                ('customers', 'reminder_email', 255),
                ('customers', 'bank_account', 255),
                ('customers', 'giro_account', 255),
                ('customers', 'vat_number', 255),
                ('customers', 'iban', 255),
                
                # Document table
                ('documents', 'file_url', 500),
                ('documents', 'file_path', 500),
            ]
            
            # Alter each column
            for table, column, size in columns_to_alter:
                try:
                    # Use raw SQL to alter the column
                    sql = text(f"ALTER TABLE {table} ALTER COLUMN {column} TYPE VARCHAR({size})")
                    db.session.execute(sql)
                    stats['altered_columns'] += 1
                    logger.info(f"Altered column {table}.{column} to VARCHAR({size})")
                except Exception as e:
                    error_msg = f"Error altering column {table}.{column}: {str(e)}"
                    logger.error(error_msg)
                    stats['errors'].append(error_msg)
            
            # Commit the changes
            db.session.commit()
            logger.info(f"Schema alteration completed: {stats['altered_columns']} columns altered")
            
        except Exception as e:
            logger.error(f"Error during schema alteration: {str(e)}")
            db.session.rollback()
            stats['errors'].append(f"Global error: {str(e)}")
        
        return stats

if __name__ == "__main__":
    logger.info("Starting schema alteration for encryption")
    
    # Run the migration
    stats = alter_schema()
    
    # Print summary
    logger.info(f"Schema alteration completed:")
    logger.info(f"  Altered columns: {stats.get('altered_columns', 0)}")
    
    if stats.get('errors'):
        logger.info(f"  Errors: {len(stats['errors'])}")
        for i, error in enumerate(stats['errors'][:10]):
            logger.info(f"    {i+1}. {error}")
        if len(stats['errors']) > 10:
            logger.info(f"    ... and {len(stats['errors']) - 10} more errors")
    
    logger.info("Schema alteration completed successfully")
