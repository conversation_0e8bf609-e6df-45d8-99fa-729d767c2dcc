"""
Migration script to encrypt existing customer data.

This script should be run after adding the encryption functionality
to encrypt existing customer data in the database.

Usage:
    python migrations/encrypt_customer_data.py

Make sure to set the ENCRYPTION_KEY environment variable before running this script.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("./logs/migration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def encrypt_data():
    """
    Encrypt sensitive data in the database.

    This function:
    1. Retrieves all customers and documents from the database
    2. For each record, encrypts sensitive fields
    3. Updates the database with the encrypted data

    Returns:
        dict: Statistics about the encryption process
    """
    # Import here to avoid circular imports
    import sys
    sys.path.append('..')  # Add parent directory to path

    # First, create the Flask application
    from app import create_app, db
    app, _ = create_app()  # Get the app and ignore the socketio return value

    # Push an application context
    with app.app_context():
        # Now import models that need app context
        from app.models.customer import Customer
        from app.models.document import Document
        from app.utils.encryption import encrypt

        stats = {
            'total_customers': 0,
            'successful_customers': 0,
            'failed_customers': 0,
            'total_documents': 0,
            'successful_documents': 0,
            'failed_documents': 0,
            'errors': []
        }

        try:
            # Check if encryption is enabled
            if not app.config.get('ENCRYPTION_ENABLED', True):
                logger.error("Encryption is disabled in the application config")
                return {
                    'error': "Encryption is disabled in the application config"
                }

            # PART 1: Encrypt Customer data
            # Get all customers
            customers = Customer.query.all()
            stats['total_customers'] = len(customers)
            logger.info(f"Found {stats['total_customers']} customers to encrypt")

            # Fields to encrypt for customers
            customer_fields_to_encrypt = [
                'address', 'postal_code', 'city',
                'address2', 'postal_code2', 'city2',
                'phone', 'mobile', 'fax', 'email',
                'invoice_email', 'reminder_email',
                'bank_account', 'giro_account', 'vat_number',
                'iban', 'bic'
            ]

            # Process each customer
            for customer in customers:
                try:
                    # For each field, encrypt the data if it exists
                    for field in customer_fields_to_encrypt:
                        value = getattr(customer, field)
                        if value:
                            # Check if the value is already encrypted
                            # This is a simple heuristic - encrypted data is base64 and longer
                            if not (isinstance(value, str) and len(value) > 100 and '=' in value):
                                encrypted_value = encrypt(value)
                                setattr(customer, field, encrypted_value)

                    # Save the customer with encrypted data
                    db.session.add(customer)
                    stats['successful_customers'] += 1

                    # Log progress periodically
                    if stats['successful_customers'] % 100 == 0:
                        logger.info(f"Processed {stats['successful_customers']} customers")
                        db.session.commit()

                except Exception as e:
                    logger.error(f"Error encrypting data for customer {customer.id}: {str(e)}")
                    stats['failed_customers'] += 1
                    stats['errors'].append(f"Customer {customer.id}: {str(e)}")

            # Commit customer changes
            db.session.commit()
            logger.info(f"Customer encryption completed: {stats['successful_customers']} successful, {stats['failed_customers']} failed")

            # PART 2: Encrypt Document data
            # Get all documents
            documents = Document.query.all()
            stats['total_documents'] = len(documents)
            logger.info(f"Found {stats['total_documents']} documents to encrypt")

            # Fields to encrypt for documents
            document_fields_to_encrypt = [
                'file_url', 'file_path'
            ]

            # Process each document
            for document in documents:
                try:
                    # For each field, encrypt the data if it exists
                    for field in document_fields_to_encrypt:
                        value = getattr(document, field)
                        if value:
                            # Check if the value is already encrypted
                            # This is a simple heuristic - encrypted data is base64 and longer
                            if not (isinstance(value, str) and len(value) > 100 and '=' in value):
                                encrypted_value = encrypt(value)
                                setattr(document, field, encrypted_value)

                    # Save the document with encrypted data
                    db.session.add(document)
                    stats['successful_documents'] += 1

                    # Log progress periodically
                    if stats['successful_documents'] % 100 == 0:
                        logger.info(f"Processed {stats['successful_documents']} documents")
                        db.session.commit()

                except Exception as e:
                    logger.error(f"Error encrypting data for document {document.id}: {str(e)}")
                    stats['failed_documents'] += 1
                    stats['errors'].append(f"Document {document.id}: {str(e)}")

            # Commit document changes
            db.session.commit()
            logger.info(f"Document encryption completed: {stats['successful_documents']} successful, {stats['failed_documents']} failed")

        except Exception as e:
            logger.error(f"Error during encryption migration: {str(e)}")
            db.session.rollback()
            stats['errors'].append(f"Global error: {str(e)}")

        return stats

if __name__ == "__main__":
    logger.info("Starting data encryption migration")

    # Check for encryption key
    if not os.getenv("ENCRYPTION_KEY"):
        logger.error("ENCRYPTION_KEY environment variable is not set")
        sys.exit(1)

    # Run the migration
    stats = encrypt_data()

    # Print summary
    logger.info(f"Migration completed:")
    logger.info(f"  Total customers: {stats.get('total_customers', 0)}")
    logger.info(f"  Successfully encrypted customers: {stats.get('successful_customers', 0)}")
    logger.info(f"  Failed customers: {stats.get('failed_customers', 0)}")
    logger.info(f"  Total documents: {stats.get('total_documents', 0)}")
    logger.info(f"  Successfully encrypted documents: {stats.get('successful_documents', 0)}")
    logger.info(f"  Failed documents: {stats.get('failed_documents', 0)}")

    if stats.get('errors'):
        logger.info(f"  Errors: {len(stats['errors'])}")
        for i, error in enumerate(stats['errors'][:10]):
            logger.info(f"    {i+1}. {error}")
        if len(stats['errors']) > 10:
            logger.info(f"    ... and {len(stats['errors']) - 10} more errors")

    if stats.get('error'):
        logger.error(f"Migration failed: {stats['error']}")
        sys.exit(1)

    logger.info("Migration completed successfully")
