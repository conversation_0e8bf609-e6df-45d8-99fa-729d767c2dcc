from flask import Blueprint, request, jsonify
from app.services.customer_service import CustomerService
from app.utils.security import token_required, role_required, roles_required
from app.utils.rate_limit import rate_limit
from app.utils.cache_decorators import cached_list, cached_detail, cached_search
from app.schemas.customer_schema import customer_schema, customers_schema
from app import limiter
import logging
from marshmallow import ValidationError

# Define the blueprint for customer-related routes
customer_bp = Blueprint("customer", __name__)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize the customer service
customer_service = CustomerService()

@customer_bp.route("", methods=["GET"], strict_slashes=False)
@role_required("administrator")
@rate_limit("60/minute")
@cached_list(entity_type="customer", timeout=300)  # Cache for 5 minutes
def get_all_customers():
    """
    Get all customers with pagination.

    Query Parameters:
        page (int): Page number (default: 1).
        per_page (int): Number of items per page (default: 20).

    Returns:
        JSON: List of customers and pagination metadata.
    """
    page = int(request.args.get("page", 1))
    per_page = int(request.args.get("per_page", 20))
    try:
        customers, total = customer_service.get_all_customers(page, per_page)
        logger.info(f"Fetched {len(customers)} customers for page {page}")
        return jsonify({
            "customers": customers,
            "total": total,
            "page": page,
            "per_page": per_page
        }), 200
    except Exception as e:
        logger.error(f"Failed to fetch customers: {str(e)}")
        return jsonify({"error": str(e)}), 500

@customer_bp.route("/<int:customer_id>", methods=["GET"], strict_slashes=False)
@role_required("administrator")
@rate_limit("60/minute")
@cached_detail(entity_type="customer", timeout=300)  # Cache for 5 minutes
def get_customer(customer_id):
    """
    Get a specific customer by ID (administrator only).

    Path Parameters:
        customer_id (int): The ID of the customer to retrieve.

    Returns:
        JSON: Customer details.
    """
    try:
        customer = customer_service.get_customer_by_id(customer_id)
        logger.info(f"Fetched customer with ID {customer_id}")
        return jsonify(customer), 200
    except Exception as e:
        logger.error(f"Failed to fetch customer {customer_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404

@customer_bp.route("/event-access/<int:customer_id>", methods=["GET"], strict_slashes=False)
@roles_required("monteur", "verkoper", "administrator")
@rate_limit("60/minute")
@cached_detail(entity_type="customer_event_access", timeout=300)  # Cache for 5 minutes
def get_customer_with_event_access(customer_id):
    """
    Get a specific customer by ID for users with pending events for that customer.

    This endpoint allows non-administrator users to access customer details
    only if they have pending events assigned to them for this customer.

    Path Parameters:
        customer_id (int): The ID of the customer to retrieve.

    Returns:
        JSON: Customer details.
    """
    try:
        # Get the current user
        current_user = request.current_user

        # Administrators always have access
        if current_user.role != "administrator":
            from app.repositories.event_repository import EventRepository
            event_repo = EventRepository()

            # Get pending events for this user and customer
            pending_events = event_repo.get_pending_events_by_user_and_customer(
                user_id=current_user.id,
                customer_id=customer_id
            )

            # If no pending events, deny access
            if not pending_events:
                logger.warning(f"Access denied for user {current_user.email} to customer {customer_id}: No pending events")
                return jsonify({"error": "You don't have any pending events for this customer"}), 403

        customer = customer_service.get_customer_by_id(customer_id)
        logger.info(f"Fetched customer with ID {customer_id} via event access for user {current_user.email}")
        return jsonify(customer), 200
    except Exception as e:
        logger.error(f"Failed to fetch customer {customer_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404

@customer_bp.route("", methods=["POST"], strict_slashes=False)
@role_required("administrator")
@rate_limit("30/minute")
def create_customer():
    """
    Create a new customer.

    Request Body: Customer data object containing all customer fields
    Returns: JSON: The created customer
    """
    data = request.get_json()
    if not data:
        logger.warning("Create customer failed: No data provided")
        return jsonify({"error": "No data provided"}), 400

    try:
        # Validate the input data using the schema
        errors = customer_schema.validate(data)
        if errors:
            logger.warning(f"Create customer validation failed: {errors}")
            return jsonify({"error": "Validation failed", "details": errors}), 400

        # Create the customer
        customer = customer_service.create_customer(data)
        logger.info(f"Created customer: {customer['name']} (ID: {customer['id']})")
        return jsonify(customer), 201
    except ValidationError as e:
        logger.warning(f"Create customer validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to create customer: {str(e)}")
        return jsonify({"error": str(e)}), 400

@customer_bp.route("/<int:customer_id>", methods=["PUT"], strict_slashes=False)
@role_required("administrator")
@rate_limit("30/minute")
def update_customer(customer_id: int):
    """
    Update an existing customer.

    Request Body: Customer data object containing fields to update
    Returns: JSON: The updated customer
    """
    data = request.get_json()
    if not data:
        logger.warning(f"Update customer {customer_id} failed: No data provided")
        return jsonify({"error": "No data provided"}), 400

    try:
        # Get the existing customer to validate partial updates
        existing_customer = customer_service.get_customer_by_id(customer_id)
        if not existing_customer:
            logger.warning(f"Update customer failed: Customer {customer_id} not found")
            return jsonify({"error": f"Customer with ID {customer_id} not found"}), 404

        # Validate the input data using the schema (partial=True for partial updates)
        errors = customer_schema.validate(data, partial=True)
        if errors:
            logger.warning(f"Update customer validation failed: {errors}")
            return jsonify({"error": "Validation failed", "details": errors}), 400

        # Update the customer
        customer = customer_service.update_customer(customer_id, data)
        logger.info(f"Updated customer with ID {customer_id}")
        return jsonify(customer), 200
    except ValidationError as e:
        logger.warning(f"Update customer validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to update customer {customer_id}: {str(e)}")
        return jsonify({"error": str(e)}), 400

@customer_bp.route("/<int:customer_id>", methods=["DELETE"], strict_slashes=False)
@role_required("administrator")
@rate_limit("30/minute")
def delete_customer(customer_id):
    """
    Delete a customer by ID.

    Path Parameters:
        customer_id (int): The ID of the customer to delete.

    Returns:
        JSON: Success message.
    """
    try:
        customer_service.delete_customer(customer_id)
        logger.info(f"Deleted customer with ID {customer_id}")
        return jsonify({"message": "Customer deleted successfully"}), 200
    except Exception as e:
        logger.error(f"Failed to delete customer {customer_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404

@customer_bp.route("/bulk-delete", methods=["POST"], strict_slashes=False)
@role_required("administrator")
@rate_limit("10/minute")
def bulk_delete_customers():
    """
    Delete multiple customers by their IDs.

    Request Body:
        customer_ids (list): List of customer IDs to delete.

    Returns:
        JSON: Success message with count of deleted customers.
    """
    try:
        data = request.get_json()
        if not data or 'customer_ids' not in data:
            return jsonify({"error": "customer_ids is required"}), 400

        customer_ids = data['customer_ids']
        if not isinstance(customer_ids, list) or not customer_ids:
            return jsonify({"error": "customer_ids must be a non-empty list"}), 400

        # Validate that all IDs are integers
        try:
            customer_ids = [int(id) for id in customer_ids]
        except (ValueError, TypeError):
            return jsonify({"error": "All customer_ids must be valid integers"}), 400

        # Delete customers
        deleted_count, failed_count = customer_service.bulk_delete_customers(customer_ids)

        logger.info(f"Bulk deleted {deleted_count} customers (failed: {failed_count})")
        return jsonify({
            "message": f"Successfully deleted {deleted_count} customers. Failed: {failed_count}.",
            "deleted": deleted_count,
            "failed": failed_count
        }), 200
    except Exception as e:
        logger.error(f"Failed to bulk delete customers: {str(e)}")
        return jsonify({"error": str(e)}), 500

@customer_bp.route("/all", methods=["DELETE"], strict_slashes=False)
@role_required("administrator")
@rate_limit("5/minute")
def delete_all_customers():
    """
    Delete ALL customers in the database.

    Returns:
        JSON: Success message with count of deleted customers.
    """
    try:
        # Get total count first for confirmation
        total_count = customer_service.get_total_customer_count()

        if total_count == 0:
            return jsonify({
                "message": "No customers to delete.",
                "deleted": 0,
                "failed": 0
            }), 200

        # Delete all customers
        deleted_count, failed_count = customer_service.delete_all_customers()

        logger.info(f"Deleted all customers: {deleted_count} deleted, {failed_count} failed")
        return jsonify({
            "message": f"Successfully deleted {deleted_count} customers. Failed: {failed_count}.",
            "deleted": deleted_count,
            "failed": failed_count
        }), 200
    except Exception as e:
        logger.error(f"Failed to delete all customers: {str(e)}")
        return jsonify({"error": str(e)}), 500

@customer_bp.route("/search", methods=["GET"], strict_slashes=False)
@role_required("administrator")
@rate_limit("60/minute")
@cached_search(entity_type="customer", timeout=60)  # Cache for 1 minute
def search_customers():
    """
    Search for customers by name.

    Query Parameters:
        q (str): The search term to look for in customer names.

    Returns:
        JSON: List of customers matching the search term.
    """
    search_term = request.args.get("q", "")

    # If search term is empty, return all customers (limited to 100 for performance)
    if not search_term:
        try:
            # Get first page with a large number of items
            customers, _ = customer_service.get_all_customers(page=1, per_page=100)
            logger.info(f"Fetched all customers for dropdown, found {len(customers)} results")
            return jsonify({"customers": customers}), 200
        except Exception as e:
            logger.error(f"Failed to fetch all customers: {str(e)}")
            return jsonify({"error": str(e)}), 500

    try:
        customers = customer_service.search_customers_by_name(search_term)
        logger.info(f"Searched for customers with term '{search_term}', found {len(customers)} results")
        return jsonify({"customers": customers}), 200
    except Exception as e:
        logger.error(f"Failed to search customers: {str(e)}")
        return jsonify({"error": str(e)}), 500


