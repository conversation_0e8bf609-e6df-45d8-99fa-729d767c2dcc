# Field-Level Encryption

This document describes the field-level encryption implementation for protecting sensitive customer data in the Customer Management application.

## Overview

Field-level encryption provides an additional layer of security by encrypting sensitive data at rest in the database. Even if an attacker gains access to the database, they cannot read the encrypted data without the encryption key.

## Implementation Details

### Encryption Algorithm

- **Algorithm**: AES-256-GCM (Galois/Counter Mode)
- **Key Derivation**: PBKDF2 with SHA-256
- **Nonce**: 12 bytes (96 bits), randomly generated for each encryption operation
- **Authentication**: GCM provides built-in authentication

### Encrypted Fields

#### Customer Model
The following customer fields are encrypted:

##### Personal Information
- Address
- Postal Code
- City
- Address2
- Postal Code2
- City2

##### Contact Information
- Phone
- Mobile
- Fax
- Email
- Invoice Email
- Reminder Email

##### Financial Information
- Bank Account
- Giro Account
- VAT Number
- IBAN
- BIC

#### Document Model
The following document fields are encrypted:

- File URL
- File Path

### How It Works

1. **Encryption Process**:
   - Data is serialized to JSON if it's not a string
   - A random nonce is generated
   - The data is encrypted using AES-256-GCM
   - The nonce is prepended to the ciphertext
   - The result is encoded as base64 and stored in the database

2. **Decryption Process**:
   - The base64-encoded data is decoded
   - The nonce is extracted from the first 12 bytes
   - The remaining bytes are decrypted using AES-256-GCM
   - The result is deserialized from JSON if possible

3. **Transparent Encryption/Decryption**:
   - A custom SQLAlchemy type (`EncryptedType`) handles encryption and decryption automatically
   - Data is encrypted when writing to the database
   - Data is decrypted when reading from the database

## Configuration

Field-level encryption is configured through environment variables:

```
# Required: Encryption key (at least 32 characters)
ENCRYPTION_KEY=your_strong_encryption_key_here

# Optional: Base64-encoded salt for key derivation
ENCRYPTION_SALT=

# Optional: Enable/disable encryption (default: True)
ENCRYPTION_ENABLED=True
```

## Key Management

### Key Generation

To generate a secure encryption key:

```python
import os
import base64
key = os.urandom(32)  # 32 bytes = 256 bits
print(base64.b64encode(key).decode('utf-8'))
```

### Key Rotation

The application includes a key rotation utility to re-encrypt data with a new key:

```bash
# Using the Flask CLI
flask rotate-encryption-key --old-key OLD_KEY --new-key NEW_KEY [--old-salt OLD_SALT] [--new-salt NEW_SALT]
```

## Security Considerations

1. **Key Storage**: The encryption key should be stored securely, separate from the database.
2. **Memory Protection**: Keys are kept in memory only as long as necessary.
3. **Encryption Overhead**: Encrypted data is larger than the original data (approximately 1.5-2x).
4. **Search Limitations**: Encrypted fields cannot be efficiently searched without decryption.

## Migration

To encrypt existing data, run the migration script:

```bash
python -m migrations.encrypt_customer_data
```

Make sure to set the `ENCRYPTION_KEY` environment variable before running the migration.

## Best Practices

1. **Regular Key Rotation**: Rotate encryption keys periodically (e.g., every 6-12 months).
2. **Backup Keys**: Securely back up encryption keys. If keys are lost, data cannot be recovered.
3. **Limit Access**: Restrict access to encryption keys to only those who absolutely need it.
4. **Monitoring**: Monitor for unusual access patterns to encrypted data.
