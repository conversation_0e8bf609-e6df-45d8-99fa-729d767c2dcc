from app.repositories.customer_repository import CustomerRepository
from app.repositories.document_repository import DocumentRepository
from app.repositories.event_repository import EventRepository
from app.models.customer import Customer
from app.models.document import Document
from app import db
from app.utils.cache_utils import clear_cache_for_entity
from typing import List, Dict, Tuple
import logging

logger = logging.getLogger(__name__)

class CustomerService:
    def __init__(self):
        self.customer_repo = CustomerRepository()
        self.document_repo = DocumentRepository()
        self.event_repo = EventRepository()

    def get_all_customers(self, page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]:
        customers, total = self.customer_repo.get_all(page, per_page)
        return [customer.to_dict() for customer in customers], total

    def get_customer_by_id(self, customer_id: int) -> Dict:
        customer = self.customer_repo.get_by_id(customer_id)
        if not customer:
            raise Exception("Customer not found")
        return customer.to_dict()

    def create_customer(self, customer_data: Dict) -> Dict:
        if 'gender' in customer_data:
            Customer.validate_gender(customer_data['gender'])
        customer = self.customer_repo.create(customer_data)

        # Clear cache
        clear_cache_for_entity('customer')

        return customer.to_dict()

    def update_customer(self, customer_id: int, customer_data: Dict) -> Dict:
        if 'gender' in customer_data:
            Customer.validate_gender(customer_data['gender'])
        customer = self.customer_repo.get_by_id(customer_id)
        if not customer:
            raise Exception("Customer not found")
        updated_customer = self.customer_repo.update(customer, customer_data)

        # Clear cache
        clear_cache_for_entity('customer', customer_id)

        return updated_customer.to_dict()

    def delete_customer(self, customer_id: int) -> bool:
        # First check if the customer exists
        customer = self.customer_repo.get_by_id(customer_id)
        if not customer:
            raise Exception("Customer not found")

        # Get all documents for this customer
        documents = Document.query.filter_by(customer_id=customer_id).all()

        # Get all events for this customer
        from app.models.event import Event
        events = Event.query.filter_by(customer_id=customer_id).all()

        # Delete all documents first
        for document in documents:
            logger.info(f"Deleting document {document.id} for customer {customer_id}")
            try:
                # Delete any sub-documents first
                sub_documents = Document.query.filter_by(related_document_id=document.id).all()
                for sub_doc in sub_documents:
                    db.session.delete(sub_doc)
                    logger.info(f"Deleted sub-document {sub_doc.id} for document {document.id}")

                # Then delete the document itself
                db.session.delete(document)
            except Exception as e:
                logger.error(f"Error deleting document {document.id}: {str(e)}")
                raise Exception(f"Failed to delete associated documents: {str(e)}")

        # Delete all events for this customer
        for event in events:
            logger.info(f"Deleting event {event.id} for customer {customer_id}")
            try:
                db.session.delete(event)
            except Exception as e:
                logger.error(f"Error deleting event {event.id}: {str(e)}")
                raise Exception(f"Failed to delete associated events: {str(e)}")

        # Now delete the customer
        try:
            db.session.delete(customer)
            db.session.commit()

            # Clear cache
            clear_cache_for_entity('customer', customer_id)

            return True
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error deleting customer {customer_id}: {str(e)}")
            raise Exception(f"Failed to delete customer: {str(e)}")

    def bulk_delete_customers(self, customer_ids: List[int]) -> Tuple[int, int]:
        """
        Delete multiple customers by their IDs.

        Args:
            customer_ids: List of customer IDs to delete

        Returns:
            Tuple of (deleted_count, failed_count)
        """
        deleted_count = 0
        failed_count = 0

        for customer_id in customer_ids:
            try:
                self.delete_customer(customer_id)
                deleted_count += 1
            except Exception as e:
                logger.error(f"Failed to delete customer {customer_id}: {str(e)}")
                failed_count += 1

        # Clear cache for all customers
        clear_cache_for_entity('customer')

        return deleted_count, failed_count

    def get_total_customer_count(self) -> int:
        """
        Get the total number of customers in the database.

        Returns:
            int: Total number of customers
        """
        return Customer.query.count()

    def delete_all_customers(self) -> Tuple[int, int]:
        """
        Delete ALL customers in the database.

        Returns:
            Tuple of (deleted_count, failed_count)
        """
        # Get all customer IDs
        all_customers = Customer.query.with_entities(Customer.id).all()
        customer_ids = [customer.id for customer in all_customers]

        if not customer_ids:
            return 0, 0

        logger.info(f"Starting deletion of all {len(customer_ids)} customers")

        # Use the existing bulk delete method for consistency
        deleted_count, failed_count = self.bulk_delete_customers(customer_ids)

        logger.info(f"Completed deletion of all customers: {deleted_count} deleted, {failed_count} failed")

        return deleted_count, failed_count

    def search_customers_by_name(self, search_term: str) -> List[Dict]:
        """Search for customers by name.

        Args:
            search_term: The search term to look for in customer names.

        Returns:
            A list of customer dictionaries matching the search term.
        """
        customers = self.customer_repo.search_by_name(search_term)
        return [customer.to_dict() for customer in customers]
