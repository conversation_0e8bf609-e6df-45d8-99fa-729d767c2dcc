# Security Test Injections for AMSPM Customer Management

## Firebase Authentication Tests

### User Login Tests

```powershell
# Test SQL Injection in Firebase Authentication
curl.exe -k -X POST https://localhost:5000/api/auth/verify -H "Content-Type: application/json" -d "{\"token\":\"' OR 1=1--\"}"

# Test XSS in Token Verification
curl.exe -k -X POST https://localhost:5000/api/auth/verify -H "Content-Type: application/json" -d "{\"token\":\"<script>alert('XSS')</script>\"}"

# Test JWT Token Tampering
# 1. Login normally and capture the token
# 2. Decode the token at jwt.io
# 3. Modify the payload (e.g., change role to "administrator")
# 4. Re-encode with 'none' algorithm
# 5. Send the modified token
curl.exe -k -X POST https://localhost:5000/api/auth/verify -H "Content-Type: application/json" -d "{\"token\":\"[MODIFIED_TOKEN]\"}"

# Test for CSRF in Authentication
# Create a form that submits to the logout endpoint
@"
<html>
  <body>
    <form action="https://localhost:5000/api/auth/logout" method="POST">
      <input type="submit" value="Click me!">
    </form>
    <script>
      document.forms[0].submit();
    </script>
  </body>
</html>
"@ | Out-File -FilePath csrf_test_logout.html
```

### User Creation Tests

```powershell
# Test SQL Injection in User Creation (Administrator only)
curl.exe -k -X POST https://localhost:5000/api/users -H "Content-Type: application/json" -H "Cookie: auth_token=<admin_token>" -d "{\"email\":\"<EMAIL>' OR 1=1--\",\"password\":\"password123\",\"role\":\"administrator\",\"name\":\"Test User\"}"

# Test XSS in User Creation
curl.exe -k -X POST https://localhost:5000/api/users -H "Content-Type: application/json" -H "Cookie: auth_token=<admin_token>" -d "{\"email\":\"<EMAIL>\",\"password\":\"password123\",\"role\":\"administrator\",\"name\":\"<script>alert('XSS')</script>\"}"

# Test Role Manipulation
curl.exe -k -X POST https://localhost:5000/api/users -H "Content-Type: application/json" -H "Cookie: auth_token=<admin_token>" -d "{\"email\":\"<EMAIL>\",\"password\":\"password123\",\"role\":\"super_admin\",\"name\":\"Test User\"}"
```

## Authorization Tests

### Role-Based Access Control Tests

```powershell
# Test accessing administrator-only endpoints as non-admin
curl.exe -k -X GET https://localhost:5000/api/users -H "Cookie: auth_token=<non_admin_token>"

# Test updating user role as non-admin
curl.exe -k -X PUT https://localhost:5000/api/users/1/role -H "Content-Type: application/json" -H "Cookie: auth_token=<non_admin_token>" -d "{\"role\":\"administrator\"}"

# Test accessing user dashboard as unauthenticated user
curl.exe -k -X GET https://localhost:5000/api/users/current -H "Cookie: auth_token=invalid_token"
```

### Permission Tests

```powershell
# Test accessing document types without permission
curl.exe -k -X GET https://localhost:5000/api/documents?document_type=beveiligingscertificaat -H "Cookie: auth_token=<limited_user_token>"

# Test direct object reference vulnerability
curl.exe -k -X GET https://localhost:5000/api/documents/123 -H "Cookie: auth_token=<limited_user_token>"

# Test permission bypass by manipulating document type
curl.exe -k -X GET https://localhost:5000/api/documents?document_type=../admin_documents -H "Cookie: auth_token=<limited_user_token>"
```

## Token Security Tests

### HttpOnly Cookie Tests

```javascript
// Run in browser console after login
// Should return empty if HttpOnly is properly set
document.cookie.match(/auth_token=([^;]+)/)

// Test if token is stored in localStorage (should not be)
localStorage.getItem('auth_token')
localStorage.getItem('user')
```

### Token Expiration Tests

```powershell
# 1. Login and capture the token
# 2. Wait for token to expire (24 hours)
# 3. Try to use the expired token
curl.exe -k -X GET https://localhost:5000/api/users/current -H "Cookie: auth_token=<expired_token>"
```

## Rate Limiting Tests

```powershell
# Create a script to test rate limiting on login verification
@"
import requests
import time

url = "https://localhost:5000/api/auth/verify"
headers = {"Content-Type": "application/json"}
data = {"token": "invalid_token"}

start_time = time.time()
success_count = 0
failure_count = 0

for i in range(100):  # Try 100 requests
    try:
        response = requests.post(url, json=data, headers=headers, verify=False)
        print(f"Request {i+1}: Status {response.status_code}")
        if response.status_code == 429:  # Rate limit exceeded
            failure_count += 1
        else:
            success_count += 1
    except Exception as e:
        print(f"Error: {e}")
    
    # Small delay to avoid overwhelming the server
    time.sleep(0.1)

elapsed = time.time() - start_time
print(f"Completed in {elapsed:.2f} seconds")
print(f"Successful requests: {success_count}")
print(f"Rate limited requests: {failure_count}")
"@ | Out-File -FilePath rate_limit_test.py

python rate_limit_test.py
```

## Firebase Storage Tests

```powershell
# Test accessing Firebase storage directly
# 1. Upload a document through the application
# 2. Capture the URL from the response
# 3. Try to access it directly without authentication
curl.exe -k -X GET "<captured_file_url>"

# Test for URL manipulation in storage paths
curl.exe -k -X GET "https://mock-storage.googleapis.com/../admin_documents/sensitive.pdf"
```

## Browser Console Tests

```javascript
// Test for user data in memory
Object.keys(localStorage).forEach(key => {
  console.log(`${key}: ${localStorage.getItem(key)}`);
});

// Test for session data
Object.keys(sessionStorage).forEach(key => {
  console.log(`${key}: ${sessionStorage.getItem(key)}`);
});

// Test for cookies (auth_token should not be visible if HttpOnly)
console.log(document.cookie);

// Test for CSRF token
console.log(document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'));
```

## Frontend Validation Bypass

```javascript
// Disable frontend validation by overriding the validation function
// Run in browser console before submitting forms
window.validateData = async () => ({ isValid: true, errors: [] });
```
