import os
import re

def find_controller_files(directory):
    """Find all controller files in the given directory."""
    controller_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('_controller.py'):
                controller_files.append(os.path.join(root, file))
    return controller_files

def update_rate_limits(file_path):
    """Update rate limits in the given file."""
    with open(file_path, 'r') as file:
        content = file.read()
    
    # Replace high rate limits with more reasonable values
    content = re.sub(r'@rate_limit\("100000/minute"\)', '@rate_limit("60/minute")', content)
    content = re.sub(r'@rate_limit\("10000/minute"\)', '@rate_limit("30/minute")', content)
    
    with open(file_path, 'w') as file:
        file.write(content)
    
    print(f"Updated rate limits in {file_path}")

def main():
    controller_dir = os.path.join('backend', 'app', 'controllers')
    controller_files = find_controller_files(controller_dir)
    
    for file_path in controller_files:
        update_rate_limits(file_path)
    
    print(f"Updated rate limits in {len(controller_files)} controller files")

if __name__ == "__main__":
    main()
