# Firebase Credentials Setup Guide

This guide explains how to set up Firebase credentials for the Customer Management Application.

## Prerequisites

1. A Firebase project with Authentication enabled
2. Admin access to the Firebase project

## Getting Firebase Service Account Credentials

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Click on the gear icon (⚙️) next to "Project Overview" to open Project settings
4. Go to the "Service accounts" tab
5. Click "Generate new private key" button
6. Save the downloaded JSON file securely

## Setting Up Credentials in the Application

### Backend Credentials

There are two ways to provide Firebase Admin SDK credentials to the backend application:

### Option 1: Using a Credentials File (Recommended for Development)

1. Create a directory called `secrets` in the `backend` folder if it doesn't exist already
2. Copy your Firebase service account JSON file to this directory
3. Rename it to `firebase-service-account-key.json` or keep the original name
4. Update your `.env` file with the path to this file:
   ```
   FIREBASE_CREDENTIALS_PATH=./secrets/firebase-service-account-key.json
   ```

### Option 2: Using Environment Variables (Recommended for Production)

1. Convert your Firebase service account JSON file to a single-line JSON string using the provided script:
   ```
   cd backend
   python convert_firebase_credentials.py secrets/firebase-service-account-key.json
   ```
2. Copy the generated JSON string and add it to your `.env` file:
   ```
   FIREBASE_CREDENTIALS_JSON={"type":"service_account","project_id":"your-project-id","private_key_id":"your-private-key-id","private_key":"your-private-key","client_email":"*****************","client_id":"your-client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"your-client-cert-url","universe_domain":"googleapis.com"}
   ```
3. Comment out or remove the `FIREBASE_CREDENTIALS_PATH` line in your `.env` file

### Frontend Configuration

For the frontend, you need to set up the Firebase Web SDK configuration:

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Click on the gear icon (⚙️) next to "Project Overview" to open Project settings
4. Go to the "General" tab
5. Scroll down to "Your apps" section and find your web app (or create one if needed)
6. Copy the Firebase configuration object
7. Add each value to your frontend `.env` file:
   ```
   VITE_FIREBASE_API_KEY=your_api_key
   VITE_FIREBASE_AUTH_DOMAIN=your_auth_domain
   VITE_FIREBASE_PROJECT_ID=your_project_id
   VITE_FIREBASE_STORAGE_BUCKET=your_storage_bucket
   VITE_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
   VITE_FIREBASE_APP_ID=your_app_id
   VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id
   ```

> **Note:** While these values are technically public (they are visible in the browser), it's still a good practice to keep them in environment variables and not hardcode them in your source code.

## Setting Up Admin User

1. Create a user in Firebase Authentication with the email you want to use as admin
2. Add the admin email to your `.env` file:
   ```
   ADMIN_EMAIL=<EMAIL>
   ```
3. Run the `set_custom_claims.py` script to set the admin role:
   ```
   cd backend
   python set_custom_claims.py
   ```

## Security Best Practices

1. **Never commit Firebase credentials to version control**
   - Make sure `secrets/` directory and `.env` files are in your `.gitignore`
   - If you accidentally committed credentials, rotate them immediately

2. Keep your `.env` files secure and never share them
   - Use different `.env` files for development and production
   - Store production credentials in a secure environment variable system

3. Use different service accounts for development and production
   - Create separate Firebase projects for development and production
   - Use different service accounts with appropriate permissions

4. Regularly rotate your service account keys
   - Generate new keys periodically (e.g., every 90 days)
   - Update your environment variables after rotation

5. Set up proper Firebase Security Rules
   - Restrict access to your Firebase resources
   - Use authentication and authorization checks
   - Test your security rules thoroughly

6. Implement proper error handling
   - Don't expose sensitive information in error messages
   - Log authentication errors for monitoring

## Troubleshooting

If you encounter issues with Firebase authentication:

1. Check that your credentials file or JSON string is correctly formatted
2. Verify that the service account has the necessary permissions
3. Make sure the Firebase project has Authentication enabled
4. Check the application logs for specific error messages

For more information, refer to the [Firebase Admin SDK documentation](https://firebase.google.com/docs/admin/setup).
