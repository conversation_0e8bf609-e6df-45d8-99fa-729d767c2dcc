import api from "../api";
import { Customer } from "../types/customer";

interface GetAllCustomersResponse {
  customers: Customer[];
  total: number;
  page: number;
  per_page: number;
}

export const getAllCustomers = async (page: number = 1, perPage: number = 20): Promise<GetAllCustomersResponse> => {
  const response = await api.get(`/customers?page=${page}&per_page=${perPage}`);
  return response.data;
};

export const getCustomerById = async (customerId: number): Promise<Customer> => {
  const response = await api.get(`/customers/${customerId}`);
  return response.data;
};

export const getCustomerWithEventAccess = async (customerId: number): Promise<Customer> => {
  const response = await api.get(`/customers/event-access/${customerId}`);
  return response.data;
};

export const createCustomer = async (customerData: Partial<Customer>): Promise<Customer> => {
  const response = await api.post("/customers", customerData);
  return response.data;
};

export const updateCustomer = async (customerId: number, customerData: Partial<Customer>): Promise<Customer> => {
  const response = await api.put(`/customers/${customerId}`, customerData);
  return response.data;
};

export const deleteCustomer = async (customerId: number): Promise<void> => {
  await api.delete(`/customers/${customerId}`);
};

export const bulkDeleteCustomers = async (customerIds: number[]): Promise<{ deleted: number; failed: number }> => {
  const response = await api.post("/customers/bulk-delete", { customer_ids: customerIds });
  return response.data;
};

export const deleteAllCustomers = async (): Promise<{ deleted: number; failed: number }> => {
  const response = await api.delete("/customers/all");
  return response.data;
};

export interface SearchCustomersResponse {
  customers: Customer[];
}

export const searchCustomers = async (searchTerm: string): Promise<SearchCustomersResponse> => {
  // Use the dedicated search endpoint
  const response = await api.get(`/customers/search?q=${encodeURIComponent(searchTerm)}`);
  return response.data;
};

export const getAllCustomersNoPage = async (): Promise<SearchCustomersResponse> => {
  // Get all customers without pagination for dropdown lists
  const response = await api.get('/customers/search?q=');
  return response.data;
};
